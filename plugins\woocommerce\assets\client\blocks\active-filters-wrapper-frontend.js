(globalThis.webpackChunkwebpackWcBlocksFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksFrontendJsonp||[]).push([[821],{3076:(e,t,r)=>{"use strict";r.d(t,{A:()=>i}),r(9300);var s=r(790);const i=({children:e})=>(0,s.jsx)("div",{className:"wc-block-filter-title-placeholder",children:e})},5479:(e,t,r)=>{"use strict";r.d(t,{G:()=>c});var s=r(7594),i=r(7143),l=r(6087),n=r(4556),o=r(3578),a=r(3993);const c=e=>{const{namespace:t,resourceName:r,resourceValues:c=[],query:u={},shouldSelect:d=!0}=e;if(!t||!r)throw new Error("The options object must have valid values for the namespace and the resource properties.");const m=(0,l.useRef)({results:[],isLoading:!0}),y=(0,n.c)(u),p=(0,n.c)(c),f=(0,o.a)(),g=(0,i.useSelect)((e=>{if(!d)return null;const i=e(s.COLLECTIONS_STORE_KEY),l=[t,r,y,p],n=i.getCollectionError(...l);if(n){if(!(0,a.isError)(n))throw new Error("TypeError: `error` object is not an instance of Error constructor");f(n)}return{results:i.getCollection(...l),isLoading:!i.hasFinishedResolution("getCollection",l)}}),[t,r,p,y,d,f]);return null!==g&&(m.current=g),m.current}},9415:(e,t,r)=>{"use strict";r.d(t,{dJ:()=>o,xd:()=>a});var s=r(7594),i=r(7143),l=r(6087),n=(r(923),r(2233));const o=e=>{const t=(0,n._)();e=e||t;const r=(0,i.useSelect)((t=>t(s.QUERY_STATE_STORE_KEY).getValueForQueryContext(e,void 0)),[e]),{setValueForQueryContext:o}=(0,i.useDispatch)(s.QUERY_STATE_STORE_KEY);return[r,(0,l.useCallback)((t=>{o(e,t)}),[e,o])]},a=(e,t,r)=>{const o=(0,n._)();r=r||o;const a=(0,i.useSelect)((i=>i(s.QUERY_STATE_STORE_KEY).getValueForQueryKey(r,e,t)),[r,e]),{setQueryValue:c}=(0,i.useDispatch)(s.QUERY_STATE_STORE_KEY);return[a,(0,l.useCallback)((t=>{c(r,e,t)}),[r,e,c])]}},2233:(e,t,r)=>{"use strict";r.d(t,{_:()=>l});var s=r(6087);const i=(0,s.createContext)("page"),l=()=>(0,s.useContext)(i);i.Provider},4556:(e,t,r)=>{"use strict";r.d(t,{c:()=>n});var s=r(6087),i=r(923),l=r.n(i);function n(e){const t=(0,s.useRef)(e);return l()(e,t.current)||(t.current=e),t.current}},41:(e,t,r)=>{"use strict";r.d(t,{p:()=>c});var s=r(4921),i=r(3993),l=r(7356),n=r(9786);function o(e={}){const t={};return(0,n.getCSSRules)(e,{selector:""}).forEach((e=>{t[e.key]=e.value})),t}function a(e,t){return e&&t?`has-${(0,l.c)(t)}-${e}`:""}const c=e=>{const t=(e=>{const t=(0,i.isObject)(e)?e:{style:{}};let r=t.style;return(0,i.isString)(r)&&(r=JSON.parse(r)||{}),(0,i.isObject)(r)||(r={}),{...t,style:r}})(e),r=function(e){const{backgroundColor:t,textColor:r,gradient:l,style:n}=e,c=a("background-color",t),u=a("color",r),d=function(e){if(e)return`has-${e}-gradient-background`}(l),m=d||n?.color?.gradient;return{className:(0,s.A)(u,d,{[c]:!m&&!!c,"has-text-color":r||n?.color?.text,"has-background":t||n?.color?.background||l||n?.color?.gradient,"has-link-color":(0,i.isObject)(n?.elements?.link)?n?.elements?.link?.color:void 0}),style:o({color:n?.color||{}})}}(t),l=function(e){const t=e.style?.border||{};return{className:function(e){const{borderColor:t,style:r}=e,i=t?a("border-color",t):"";return(0,s.A)({"has-border-color":!!t||!!r?.border?.color,[i]:!!i})}(e),style:o({border:t})}}(t),n=function(e){return{className:void 0,style:o({spacing:e.style?.spacing||{}})}}(t),c=(e=>{const t=(0,i.isObject)(e.style.typography)?e.style.typography:{},r=(0,i.isString)(t.fontFamily)?t.fontFamily:"";return{className:e.fontFamily?`has-${e.fontFamily}-font-family`:r,style:{fontSize:e.fontSize?`var(--wp--preset--font-size--${e.fontSize})`:t.fontSize,fontStyle:t.fontStyle,fontWeight:t.fontWeight,letterSpacing:t.letterSpacing,lineHeight:t.lineHeight,textDecoration:t.textDecoration,textTransform:t.textTransform}}})(t);return{className:(0,s.A)(c.className,r.className,l.className,n.className),style:{...c.style,...r.style,...l.style,...n.style}}}},3578:(e,t,r)=>{"use strict";r.d(t,{a:()=>i});var s=r(6087);const i=()=>{const[,e]=(0,s.useState)();return(0,s.useCallback)((t=>{e((()=>{throw t}))}),[])}},7681:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>R});var s=r(4921),i=r(41),l=r(3993),n=r(7723),o=r(9415),a=r(5703),c=r(6087),u=r(4656),d=r(5009),m=r(3076);r(4756);var y=r(3945),p=r(910),f=r(3832),g=r(4530),h=r(1924);const b=JSON.parse('{"uK":{"O":{"A":"list"},"F":{"A":3}}}');var _=r(790);const S=(e,t)=>Number.isFinite(e)&&Number.isFinite(t)?(0,n.sprintf)(/* translators: %1$s min price, %2$s max price */ /* translators: %1$s min price, %2$s max price */
(0,n.__)("Between %1$s and %2$s","woocommerce"),(0,p.formatPrice)(e),(0,p.formatPrice)(t)):Number.isFinite(e)?(0,n.sprintf)(/* translators: %s min price */ /* translators: %s min price */
(0,n.__)("From %s","woocommerce"),(0,p.formatPrice)(e)):(0,n.sprintf)(/* translators: %s max price */ /* translators: %s max price */
(0,n.__)("Up to %s","woocommerce"),(0,p.formatPrice)(t)),w=({type:e,name:t,prefix:r="",removeCallback:s=()=>null,showLabel:i=!0,displayStyle:l})=>{const o=r?(0,_.jsxs)(_.Fragment,{children:[r," ",t]}):t,a=(0,n.sprintf)(/* translators: 1: filter type, 2: attribute value used in the filter. For example: Remove Size Large filter. */ /* translators: 1: filter type, 2: attribute value used in the filter. For example: Remove Size Large filter. */
(0,n.__)("Remove %1$s %2$s filter","woocommerce"),e,t);return(0,_.jsxs)("li",{className:"wc-block-active-filters__list-item",children:[i&&(0,_.jsx)("span",{className:"wc-block-active-filters__list-item-type",children:e+": "}),"chips"===l?(0,_.jsx)(u.RemovableChip,{element:"span",text:o,onRemove:s,radius:"large",ariaLabel:a}):(0,_.jsxs)("span",{className:"wc-block-active-filters__list-item-name",children:[(0,_.jsxs)("button",{className:"wc-block-active-filters__list-item-remove",onClick:s,children:[(0,_.jsx)(g.A,{className:"wc-block-components-chip__remove-icon",icon:h.A,size:16}),(0,_.jsx)(u.Label,{screenReaderLabel:a})]}),o]})]},e+":"+t)},x=(...e)=>{if(!window)return;const t=window.location.href,r=(0,f.getQueryArgs)(t),s=(0,f.removeQueryArgs)(t,...Object.keys(r));e.forEach((e=>{if("string"==typeof e)return delete r[e];if("object"==typeof e){const t=Object.keys(e)[0],s=r[t].toString().split(",");r[t]=s.filter((r=>r!==e[t])).join(",")}}));const i=Object.fromEntries(Object.entries(r).filter((([,e])=>e))),l=(0,f.addQueryArgs)(s,i);(0,d.CH)(l)},v=["min_price","max_price","rating_filter","filter_","query_type_"],k=e=>{let t=!1;for(let r=0;v.length>r;r++){const s=v[r];if(s===e.substring(0,s.length)){t=!0;break}}return t};var j=r(5479),C=r(8537),N=r(9180);const A=({attributeObject:e,slugs:t=[],operator:r="in",displayStyle:s,isLoadingCallback:i})=>{const{results:u,isLoading:d}=(0,j.G)({namespace:"/wc/store/v1",resourceName:"products/attributes/terms",resourceValues:[e.id]}),[m,y]=(0,o.xd)("attributes",[]);if((0,c.useEffect)((()=>{i(d)}),[d,i]),!Array.isArray(u)||!(0,l.isAttributeTermCollection)(u)||!(0,l.isAttributeQueryCollection)(m))return null;const p=e.label,f=(0,a.getSettingWithCoercion)("isRenderingPhpTemplate",!1,l.isBoolean);return(0,_.jsxs)("li",{children:[(0,_.jsxs)("span",{className:"wc-block-active-filters__list-item-type",children:[p,":"]}),(0,_.jsx)("ul",{children:t.map(((t,i)=>{const l=u.find((e=>e.slug===t));if(!l)return null;let o="";return i>0&&"and"===r&&(o=(0,_.jsx)("span",{className:"wc-block-active-filters__list-item-operator",children:(0,n.__)("All","woocommerce")})),w({type:p,name:(0,C.decodeEntities)(l.name||t),prefix:o,isLoading:d,removeCallback:()=>{const r=m.find((({attribute:t})=>t===`pa_${e.name}`));1===r?.slug.length?x(`query_type_${e.name}`,`filter_${e.name}`):x({[`filter_${e.name}`]:t}),f||(0,N.$)(m,y,e,t)},showLabel:!1,displayStyle:s})}))})]})},E=({displayStyle:e,isLoading:t})=>t?(0,_.jsx)(_.Fragment,{children:[...Array("list"===e?2:3)].map(((t,r)=>(0,_.jsx)("li",{className:"list"===e?"show-loading-state-list":"show-loading-state-chips",children:(0,_.jsx)("span",{className:"show-loading-state__inner"})},r)))}):null;var O=r(5179);const F=({attributes:e,isEditor:t=!1})=>{const r=(0,O.LY)(),i=function(){const e=(0,c.useRef)(!1);return(0,c.useEffect)((()=>(e.current=!0,()=>{e.current=!1})),[]),(0,c.useCallback)((()=>e.current),[])}()(),p=(0,a.getSettingWithCoercion)("isRenderingPhpTemplate",!1,l.isBoolean),[g,h]=(0,c.useState)(!0),b=(()=>{if(!window)return!1;const e=window.location.href,t=(0,f.getQueryArgs)(e),r=Object.keys(t);let s=!1;for(let e=0;r.length>e;e++){const t=r[e];if(k(t)){s=!0;break}}return s})()&&!t&&g,[v,j]=(0,o.xd)("attributes",[]),[C,N]=(0,o.xd)("stock_status",[]),[F,R]=(0,o.xd)("min_price"),[L,Q]=(0,o.xd)("max_price"),[T,$]=(0,o.xd)("rating"),P=(0,a.getSetting)("stockStatusOptions",[]),K=(0,a.getSetting)("attributes",[]),Y=(0,c.useMemo)((()=>{if(b||0===C.length||!(0,l.isStockStatusQueryCollection)(C)||!(0,l.isStockStatusOptions)(P))return null;const t=(0,n.__)("Stock Status","woocommerce");return(0,_.jsxs)("li",{children:[(0,_.jsxs)("span",{className:"wc-block-active-filters__list-item-type",children:[t,":"]}),(0,_.jsx)("ul",{children:C.map((r=>w({type:t,name:P[r],removeCallback:()=>{if(x({filter_stock_status:r}),!p){const e=C.filter((e=>e!==r));N(e)}},showLabel:!1,displayStyle:e.displayStyle})))})]})}),[b,P,C,N,e.displayStyle,p]),B=(0,c.useMemo)((()=>b||!Number.isFinite(F)&&!Number.isFinite(L)?null:w({type:(0,n.__)("Price","woocommerce"),name:S(F,L),removeCallback:()=>{x("max_price","min_price"),p||(R(void 0),Q(void 0))},displayStyle:e.displayStyle})),[b,F,L,e.displayStyle,R,Q,p]),W=(0,c.useMemo)((()=>!(0,l.isAttributeQueryCollection)(v)&&i||!v.length&&!(e=>{if(!window)return!1;const t=e.map((e=>`filter_${e.attribute_name}`)),r=window.location.href,s=(0,f.getQueryArgs)(r),i=Object.keys(s);let l=!1;for(let e=0;i.length>e;e++){const r=i[e];if(t.includes(r)){l=!0;break}}return l})(K)?(g&&h(!1),null):v.map((t=>{const r=(0,y.OJ)(t.attribute);return r?(0,_.jsx)(A,{attributeObject:r,displayStyle:e.displayStyle,slugs:t.slug,operator:t.operator,isLoadingCallback:h},t.attribute):(g&&h(!1),null)}))),[v,i,K,g,e.displayStyle]);(0,c.useEffect)((()=>{if(!p)return;if(T.length&&T.length>0)return;const e=(0,d.Vf)("rating_filter")?.toString();e&&$(e.split(","))}),[p,T,$]);const V=(0,c.useMemo)((()=>{if(b||0===T.length||!(0,l.isRatingQueryCollection)(T))return null;const t=(0,n.__)("Rating","woocommerce");return(0,_.jsxs)("li",{children:[(0,_.jsxs)("span",{className:"wc-block-active-filters__list-item-type",children:[t,":"]}),(0,_.jsx)("ul",{children:T.map((r=>w({type:t,name:(0,n.sprintf)(/* translators: %s is referring to the average rating value */ /* translators: %s is referring to the average rating value */
(0,n.__)("Rated %s out of 5","woocommerce"),r),removeCallback:()=>{if(x({rating_filter:r}),!p){const e=T.filter((e=>e!==r));$(e)}},showLabel:!1,displayStyle:e.displayStyle})))})]})}),[b,T,$,e.displayStyle,p]);if(!b&&!(v.length>0||C.length>0||T.length>0||Number.isFinite(F)||Number.isFinite(L))&&!t)return r(!1),null;const z=`h${e.headingLevel}`,J=(0,_.jsx)(z,{className:"wc-block-active-filters__title",children:e.heading}),U=b?(0,_.jsx)(m.A,{children:J}):J;if(!(0,a.getSettingWithCoercion)("hasFilterableProducts",!1,l.isBoolean))return r(!1),null;r(!0);const q=(0,s.A)("wc-block-active-filters__list",{"wc-block-active-filters__list--chips":"chips"===e.displayStyle,"wc-block-active-filters--loading":b});return(0,_.jsxs)(_.Fragment,{children:[!t&&e.heading&&U,(0,_.jsxs)("div",{className:"wc-block-active-filters",children:[(0,_.jsx)("ul",{className:q,children:t?(0,_.jsxs)(_.Fragment,{children:[w({type:(0,n.__)("Size","woocommerce"),name:(0,n.__)("Small","woocommerce"),displayStyle:e.displayStyle}),w({type:(0,n.__)("Color","woocommerce"),name:(0,n.__)("Blue","woocommerce"),displayStyle:e.displayStyle})]}):(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)(E,{isLoading:b,displayStyle:e.displayStyle}),B,Y,W,V]})}),b?(0,_.jsx)("span",{className:"wc-block-active-filters__clear-all-placeholder"}):(0,_.jsx)("button",{className:"wc-block-active-filters__clear-all",onClick:()=>{(()=>{if(!window)return;const e=window.location.href,t=(0,f.getQueryArgs)(e),r=(0,f.removeQueryArgs)(e,...Object.keys(t)),s=Object.fromEntries(Object.keys(t).filter((e=>!k(e))).map((e=>[e,t[e]]))),i=(0,f.addQueryArgs)(r,s);(0,d.CH)(i)})(),p||(R(void 0),Q(void 0),j([]),N([]),$([]))},children:(0,_.jsx)(u.Label,{label:(0,n.__)("Clear All","woocommerce"),screenReaderLabel:(0,n.__)("Clear All Filters","woocommerce")})})]})]})},R=e=>{const t=(0,i.p)(e),r=(n=e,{heading:(0,l.isString)(n?.heading)?n.heading:"",headingLevel:(0,l.isString)(n?.headingLevel)&&parseInt(n.headingLevel,10)||b.uK.F.A,displayStyle:(0,l.isString)(n?.displayStyle)&&n.displayStyle||b.uK.O.A});var n;return(0,_.jsx)("div",{className:(0,s.A)((0,l.isString)(e.className)?e.className:"",t.className),style:t.style,children:(0,_.jsx)(F,{isEditor:!1,attributes:r})})}},9180:(e,t,r)=>{"use strict";r.d(t,{$:()=>i,u:()=>l});var s=r(9456);const i=(e=[],t,r,i="")=>{const l=e.filter((e=>e.attribute===r.taxonomy)),n=l.length?l[0]:null;if(!(n&&n.slug&&Array.isArray(n.slug)&&n.slug.includes(i)))return;const o=n.slug.filter((e=>e!==i)),a=e.filter((e=>e.attribute!==r.taxonomy));o.length>0&&(n.slug=o.sort(),a.push(n)),t((0,s.di)(a).asc("attribute"))},l=(e=[],t,r,i=[],l="in")=>{if(!r||!r.taxonomy)return[];const n=e.filter((e=>e.attribute!==r.taxonomy));return 0===i.length?t(n):(n.push({attribute:r.taxonomy,operator:l,slug:i.map((({slug:e})=>e)).sort()}),t((0,s.di)(n).asc("attribute"))),n}},3945:(e,t,r)=>{"use strict";r.d(t,{OJ:()=>n,WK:()=>l});var s=r(5703);r(3993),r(7143);const i=(0,s.getSetting)("attributes",[]).reduce(((e,t)=>{const r=(s=t)&&s.attribute_name?{id:parseInt(s.attribute_id,10),name:s.attribute_name,taxonomy:"pa_"+s.attribute_name,label:s.attribute_label,orderby:s.attribute_orderby}:null;var s;return r&&r.id&&e.push(r),e}),[]),l=e=>{if(e)return i.find((t=>t.id===e))},n=e=>{if(e)return i.find((t=>t.taxonomy===e))}},5009:(e,t,r)=>{"use strict";r.d(t,{CH:()=>u,Q:()=>d,Vf:()=>c,nD:()=>a,xB:()=>o});var s=r(3832),i=r(5703),l=r(3993);const n=(0,i.getSettingWithCoercion)("isRenderingPhpTemplate",!1,l.isBoolean),o="query_type_",a="filter_";function c(e){return window?(0,s.getQueryArg)(window.location.href,e):null}function u(e){if(n){const t=new URL(e);t.pathname=t.pathname.replace(/\/page\/[0-9]+/i,""),t.searchParams.delete("paged"),t.searchParams.forEach(((e,r)=>{r.match(/^query(?:-[0-9]+)?-page$/)&&t.searchParams.delete(r)})),window.location.href=t.href}else window.history.replaceState({},"",e)}const d=e=>{const t=(0,s.getQueryArgs)(e);return(0,s.addQueryArgs)(e,t)}},9300:()=>{},4756:()=>{}}]);