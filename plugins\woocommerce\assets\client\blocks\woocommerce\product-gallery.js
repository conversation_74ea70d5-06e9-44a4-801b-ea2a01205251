import*as e from"@wordpress/interactivity";var t={d:(e,o)=>{for(var r in o)t.o(o,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:o[r]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const o=(a={getContext:()=>e.getContext,getElement:()=>e.getElement,store:()=>e.store,withScope:()=>e.withScope},i={},t.d(i,a),i),r=e=>(0,o.getContext)(e),c={state:{get imageIndex(){const{imageData:e,selectedImageId:t}=r();return e.indexOf(t)}},actions:{selectImage:e=>{const t=r(),{imageData:c}=t,n=c[e],{disableLeft:a,disableRight:i}={disableLeft:0===(l=e),disableRight:l===c.length-1};var l;t.disableLeft=a,t.disableRight=i,t.selectedImageId=n,-1!==n&&((e=>{if(!e)return;const t=(0,o.getElement)()?.ref;if(!t)return;const r=t.closest(".wp-block-woocommerce-product-gallery");if(!r)return;const c=r.querySelector(`.wp-block-woocommerce-product-gallery-large-image img[data-image-id="${e}"]`);c&&c.scrollIntoView({behavior:"smooth",block:"nearest",inline:"center"})})(n),(e=>{if(!e)return;const t=(0,o.getElement)()?.ref;if(!t)return;const r=t.closest(".wp-block-woocommerce-product-gallery");if(!r)return;const c=r.querySelector(`.wc-block-product-gallery-thumbnails__thumbnail img[data-image-id="${e}"]`);if(!c)return;const n=c.closest(".wc-block-product-gallery-thumbnails__scrollable");if(!n)return;const a=c.closest(".wc-block-product-gallery-thumbnails__thumbnail");if(!a)return;const i=n.getBoundingClientRect(),l=a.getBoundingClientRect(),s=n.scrollTop+(l.top-i.top)-(i.height-l.height)/2,g=n.scrollLeft+(l.left-i.left)-(i.width-l.width)/2;n.scrollTo({top:s,left:g,behavior:"smooth"})})(n))},selectCurrentImage:e=>{e&&e.stopPropagation();const t=(0,o.getElement)()?.ref;if(!t)return;const c=t.getAttribute("data-image-id");if(!c)return;const a=parseInt(c,10),{imageData:i}=r(),l=i.indexOf(a);n.selectImage(l)},selectNextImage:e=>{e&&e.stopPropagation();const{imageData:t,selectedImageId:o}=r(),c=t.indexOf(o),a=Math.min(t.length-1,c+1);n.selectImage(a)},selectPreviousImage:e=>{e&&e.stopPropagation();const{imageData:t,selectedImageId:o}=r(),c=t.indexOf(o),a=Math.max(0,c-1);n.selectImage(a)},onSelectedLargeImageKeyDown:e=>{"Enter"!==e.code&&"Space"!==e.code&&"NumpadEnter"!==e.code||("Space"===e.code&&e.preventDefault(),n.openDialog()),"ArrowRight"===e.code&&n.selectNextImage(),"ArrowLeft"===e.code&&n.selectPreviousImage()},onThumbnailKeyDown:e=>{"Enter"!==e.code&&"Space"!==e.code&&"NumpadEnter"!==e.code||("Space"===e.code&&e.preventDefault(),n.selectCurrentImage())},onDialogKeyDown:e=>{"Escape"===e.code&&n.closeDialog()},openDialog:()=>{r().isDialogOpen=!0,document.body.classList.add("wc-block-product-gallery-dialog-open")},closeDialog:()=>{r().isDialogOpen=!1,document.body.classList.remove("wc-block-product-gallery-dialog-open")},onTouchStart:e=>{const t=r(),{clientX:o}=e.touches[0];t.touchStartX=o,t.touchCurrentX=o,t.isDragging=!0},onTouchMove:e=>{const t=r();if(!t.isDragging)return;const{clientX:o}=e.touches[0];t.touchCurrentX=o,e.preventDefault()},onTouchEnd:()=>{const e=r();if(!e.isDragging)return;const t=e.touchCurrentX-e.touchStartX,c=(0,o.getElement)()?.ref,a=c?.offsetWidth||0;Math.abs(t)>.2*a&&(t>0&&!e.disableLeft?n.selectPreviousImage():t<0&&!e.disableRight&&n.selectNextImage()),e.isDragging=!1,e.touchStartX=0,e.touchCurrentX=0},onScroll:()=>{const e=(0,o.getElement)()?.ref;if(!e)return;const t=r(),c=(e=>{if(!e)return{top:!1,bottom:!1,left:!1,right:!1};const{scrollTop:t,scrollHeight:o,clientHeight:r,scrollLeft:c,scrollWidth:n,clientWidth:a}=e;return{top:t>3,bottom:t+r<o-3,left:c>3,right:c+a<n-3}})(e);t.thumbnailsOverflow=c}},callbacks:{watchForChangesOnAddToCartForm:()=>{const e=r(),t=document.querySelector(`form[data-product_id="${e.productId}"]`);if(!t)return;const c=()=>(0,o.withScope)((()=>n.selectImage(0))),a=new MutationObserver((0,o.withScope)((function(e){for(const t of e){const{imageData:e}=r(),o=t.target.getAttribute("current-image"),c=o?parseInt(o,10):null;if("attributes"===t.type&&c&&e.includes(c)){const t=e.indexOf(c);n.selectImage(t)}else n.selectImage(0)}})));a.observe(t,{attributes:!0});const i=document.querySelector(".wp-block-add-to-cart-form .reset_variations");return i&&i.addEventListener("click",c),()=>{a.disconnect(),document.removeEventListener("click",c)}},dialogStateChange:()=>{const{selectedImageId:e,isDialogOpen:t}=r(),{ref:c}=(0,o.getElement)()||{};if(t&&c instanceof HTMLElement){c.focus();const t=c.querySelector(`[data-image-id="${e}"]`);t instanceof HTMLElement&&(t.scrollIntoView({behavior:"auto",block:"center"}),t.focus())}},toggleActiveImageAttributes:()=>{const e=(0,o.getElement)()?.ref;if(!e)return!1;const t=e.getAttribute("data-image-id");if(!t)return!1;const{selectedImageId:c}=r();c===Number(t)?(e.classList.add("is-active"),e.setAttribute("tabIndex","0")):(e.classList.remove("is-active"),e.setAttribute("tabIndex","-1"))}}},{actions:n}=(0,o.store)("woocommerce/product-gallery",c,{lock:!0});var a,i;