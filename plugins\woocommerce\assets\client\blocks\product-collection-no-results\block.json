{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "woocommerce/product-collection-no-results", "title": "No results", "category": "woocommerce", "description": "The contents of this block will display when there are no products found.", "textdomain": "woocommerce", "keywords": ["Product Collection"], "usesContext": ["queryId", "query"], "ancestor": ["woocommerce/product-collection"], "supports": {"interactivity": {"clientNavigation": true}, "align": true, "reusable": false, "html": false, "color": {"gradients": true, "link": true}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalFontWeight": true, "__experimentalFontStyle": true, "__experimentalTextTransform": true, "__experimentalTextDecoration": true, "__experimentalLetterSpacing": true, "__experimentalDefaultControls": {"fontSize": true}}}}