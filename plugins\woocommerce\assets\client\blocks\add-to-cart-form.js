(()=>{var t,e,o,i={7755:(t,e,o)=>{"use strict";const i=window.wp.blocks,n=window.wp.data,r=window.wc.wcTypes;class s{blocks=new Map;initialized=!1;attemptedRegisteredBlocks=new Set;constructor(){this.initializeSubscriptions()}static getInstance(){return s.instance||(s.instance=new s),s.instance}parseTemplateId(t){const e=(0,r.isNumber)(t)?void 0:t;return e?.split("//")[1]}initializeSubscriptions(){if(this.initialized)return;const t=(0,n.subscribe)((()=>{const e=(0,n.select)("core/edit-site"),o=(0,n.select)("core/edit-post");if(e||o)if(e){const o=e.getEditedPostId();t(),this.currentTemplateId="string"==typeof o?this.parseTemplateId(o):void 0,(0,n.subscribe)((()=>{const t=this.currentTemplateId;this.currentTemplateId=this.parseTemplateId(e.getEditedPostId()),t!==this.currentTemplateId&&this.handleTemplateChange(t)}),"core/edit-site"),this.initialized=!0}else o&&(t(),this.blocks.forEach((t=>{if(t.isAvailableOnPostEditor){const e=t.variationName||t.blockName;this.hasAttemptedRegistration(e)||this.registerBlock(t)}})),this.initialized=!0)}))}handleTemplateChange(t){(this.currentTemplateId?.includes("single-product")||t?.includes("single-product"))&&this.blocks.forEach((t=>{this.unregisterBlock(t),this.registerBlock(t)}))}hasAttemptedRegistration(t){return this.attemptedRegisteredBlocks.has(t)}unregisterBlock(t){const{blockName:e,isVariationBlock:o,variationName:n}=t;try{o&&n?((0,i.unregisterBlockVariation)(e,n),this.attemptedRegisteredBlocks.delete(n)):((0,i.unregisterBlockType)(e),this.attemptedRegisteredBlocks.delete(e))}catch(t){console.debug(`Failed to unregister block ${e}:`,t)}}registerBlock(t){const{blockName:e,settings:o,isVariationBlock:s,variationName:c,isAvailableOnPostEditor:a}=t;try{const t=c||e;if(this.hasAttemptedRegistration(t))return;const l=(0,n.select)("core/edit-site");if(!l&&!a)return;if(s)(0,i.registerBlockVariation)(e,o);else{const t=(0,r.isEmpty)(o?.ancestor)?["woocommerce/single-product"]:o?.ancestor,n=l&&this.currentTemplateId?.includes("single-product");(0,i.registerBlockType)(e,{...o,ancestor:n?void 0:t})}this.attemptedRegisteredBlocks.add(t)}catch(t){console.error(`Failed to register block ${e}:`,t)}}registerBlockConfig(t){const e=t.variationName||t.blockName;this.blocks.set(e,t),this.registerBlock(t)}}var c=o(4530),a=o(6012);const l=JSON.parse('{"name":"woocommerce/add-to-cart-form","title":"Add to Cart with Options","description":"Display a button so the customer can add a product to their cart. Options will also be displayed depending on product type. e.g. quantity, variation.","category":"woocommerce-product-elements","attributes":{"quantitySelectorStyle":{"type":"string","enum":["input","stepper"],"default":"input"}},"keywords":["WooCommerce"],"usesContext":["postId"],"textdomain":"woocommerce","supports":{"interactivity":true},"apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json","viewScriptModule":"woocommerce/add-to-cart-form","style":"file:../woocommerce/add-to-cart-form-style.css","editorStyle":"file:../woocommerce/add-to-cart-form-editor.css"}'),d=window.wp.blockEditor;var u=o(7723),p=o(4921),m=(o(2405),o(790));const b=({tag:t="div",width:e="100%",height:o="8px",maxWidth:i="",className:n="",borderRadius:r="",isStatic:s=!1})=>(0,m.jsx)(t,{className:(0,p.A)("wc-block-components-skeleton__element",{"wc-block-components-skeleton__element--static":s},n),"aria-hidden":"true",style:{width:e,height:o,borderRadius:r,maxWidth:i}}),h=({isStatic:t=!1})=>(0,m.jsxs)("div",{className:"wc-block-components-skeleton",children:[(0,m.jsx)(b,{height:"16px",isStatic:t}),(0,m.jsx)(b,{height:"16px",isStatic:t}),(0,m.jsx)(b,{height:"16px",width:"80%",isStatic:t})]}),w=window.wp.components,k=window.wc.wcSettings;let g=function(t){return t.Input="input",t.Stepper="stepper",t}({});const f=t=>t===g.Input?(0,u.__)("Shoppers can enter a number of items to add to cart.","woocommerce"):t===g.Stepper?(0,u.__)("Shoppers can use buttons to change the number of items to add to cart.","woocommerce"):void 0,y=({quantitySelectorStyle:t,setAttributes:e,features:o})=>{const{isStepperLayoutFeatureEnabled:i}=o;return(0,m.jsx)(d.InspectorControls,{children:i&&(0,m.jsx)(w.PanelBody,{title:(0,u.__)("Quantity Selector","woocommerce"),children:(0,m.jsxs)(w.__experimentalToggleGroupControl,{__nextHasNoMarginBottom:!0,value:t,isBlock:!0,onChange:t=>{e({quantitySelectorStyle:t})},help:f(t),children:[(0,m.jsx)(w.__experimentalToggleGroupControlOption,{label:(0,u.__)("Input","woocommerce"),value:g.Input}),(0,m.jsx)(w.__experimentalToggleGroupControlOption,{label:(0,u.__)("Stepper","woocommerce"),value:g.Stepper})]})})})},_=(0,k.getSettingWithCoercion)("isBlockifiedAddToCart",!1,r.isBoolean),x=(0,k.getSetting)("isBlockTheme"),v=_&&x;var S=o(6087),j=o(1244),B=o.n(j);B()("wc-admin:tracks:stats");const O=B()("wc-admin:tracks");function N({children:t,className:e,actionLabel:o,onActionClick:i,...n}){return(0,m.jsx)(w.Notice,{...n,className:(0,p.$)("wc-block-editor-components-upgrade-downgrade-notice",e),actions:[{label:o,onClick:i,noDefaultClasses:!0,variant:"link"}],children:(0,m.jsx)("div",{className:"wc-block-editor-components-upgrade-downgrade-notice__text",children:t})})}o(9969);const T=({blocks:t,findCondition:e})=>{for(const o of t){if(e(o))return o;if(o.innerBlocks){const t=T({blocks:o.innerBlocks,findCondition:e});if(t)return t}}},C=({blockClientId:t})=>{const e=(0,S.createInterpolateElement)((0,u.__)("Upgrade the Add to Cart with Options block to <strongText /> for more features!","woocommerce"),{strongText:(0,m.jsx)("strong",{children:(0,u.__)("a new blockified experience","woocommerce")})}),o=(0,u.__)("Upgrade to the blockified Add to Cart with Options block","woocommerce");return(0,m.jsx)(N,{isDismissible:!1,actionLabel:o,onActionClick:async()=>{const e=await(async t=>{const e=(0,n.select)("core/block-editor").getBlocks(),o=T({blocks:e,findCondition:e=>e.name===l.name&&e.clientId===t});if(!o)return!1;const r=(0,i.createBlock)("woocommerce/add-to-cart-with-options");return(0,n.dispatch)("core/block-editor").replaceBlock(o.clientId,r),!0})(t);e&&function(t,e){if(O("recordevent %s %o","wcadmin_"+t,e,{_tqk:window._tkq,shouldRecord:!!window._tkq&&!!window.wcTracks&&!!window.wcTracks.isEnabled}),!window.wcTracks||"function"!=typeof window.wcTracks.recordEvent)return!1;window.wcTracks.recordEvent(t,e)}("blocks_add_to_cart_with_options_migration",{transform_to:"blockified"})},children:e})};(t=>{const e=t.name;if(!e)return void console.error("registerProductBlockType: Block name is required for registration");const o=(({name:t,...e})=>e)(t),{isVariationBlock:i,variationName:n,isAvailableOnPostEditor:r,...c}={...o,...{isAvailableOnPostEditor:!0}||{}},a={blockName:e,settings:{...c},isVariationBlock:null!=i&&i,variationName:null!=n?n:void 0,isAvailableOnPostEditor:null!=r&&r};s.getInstance().registerBlockConfig(a)})({...l,edit:t=>{const{setAttributes:e}=t,o=(0,k.getSettingWithCoercion)("isStepperLayoutFeatureEnabled",!1,r.isBoolean),i=t.attributes.quantitySelectorStyle!==g.Input&&o?"wc-block-add-to-cart-form--stepper":"wc-block-add-to-cart-form--input",s=(0,d.useBlockProps)({className:`wc-block-add-to-cart-form ${i}`}),c=(0,n.useSelect)((t=>(t=>{if(!(t=>null===t)(e=t)&&e instanceof Object&&e.constructor===Object){const e=t.getEditedPostType();return"wp_template"===e||"wp_template_part"===e}var e;return!1})(t("core/edit-site"))),[]),a=(0,k.getSetting)("isBlockTheme",!1),l=a?"":"wp-block-button",p=a?"":"wp-block-button__link wc-block-components-button";return(0,m.jsxs)(m.Fragment,{children:[v&&(0,m.jsx)(d.InspectorControls,{children:(0,m.jsx)(C,{blockClientId:t?.clientId})}),(0,m.jsx)(y,{quantitySelectorStyle:t.attributes.quantitySelectorStyle,setAttributes:e,features:{isStepperLayoutFeatureEnabled:o}}),(0,m.jsx)("div",{...s,children:(0,m.jsx)(w.Tooltip,{text:(0,u.__)("Customer will see product add-to-cart options in this space, dependent on the product type.","woocommerce"),position:"bottom right",children:(0,m.jsxs)("div",{className:"wc-block-editor-add-to-cart-form-container",children:[(0,m.jsx)(h,{isStatic:!0}),(0,m.jsxs)(w.Disabled,{children:[(t.attributes.quantitySelectorStyle===g.Input||!o)&&(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)("div",{className:"quantity",children:(0,m.jsx)("input",{style:c?{}:{backgroundColor:"#ffffff",lineHeight:"normal",minHeight:"unset",boxSizing:"unset",borderRadius:"unset"},type:"number",value:"1",className:"input-text qty text",readOnly:!0})}),(0,m.jsx)("div",{className:l,children:(0,m.jsx)("button",{className:`single_add_to_cart_button alt wp-element-button ${p}`,children:(0,u.__)("Add to cart","woocommerce")})})]}),t.attributes.quantitySelectorStyle===g.Stepper&&o&&(0,m.jsxs)(m.Fragment,{children:[(0,m.jsxs)("div",{className:"quantity wc-block-components-quantity-selector",children:[(0,m.jsx)("button",{className:"wc-block-components-quantity-selector__button wc-block-components-quantity-selector__button--minus",children:"-"}),(0,m.jsx)("input",{style:c?{}:{backgroundColor:"#ffffff",lineHeight:"normal",minHeight:"unset",boxSizing:"unset",borderRadius:"unset"},type:"number",value:"1",className:"input-text qty text",readOnly:!0}),(0,m.jsx)("button",{className:"wc-block-components-quantity-selector__button wc-block-components-quantity-selector__button--plus",children:"+"})]}),(0,m.jsx)("div",{className:l,children:(0,m.jsx)("button",{className:`single_add_to_cart_button alt wp-element-button ${p}`,children:(0,u.__)("Add to cart","woocommerce")})})]})]})]})})})]})},icon:{src:(0,m.jsx)(c.A,{icon:a.A,className:"wc-block-editor-components-block-icon"})},ancestor:["woocommerce/single-product"],save:()=>null})},2405:()=>{},9969:()=>{},1609:t=>{"use strict";t.exports=window.React},790:t=>{"use strict";t.exports=window.ReactJSXRuntime},6087:t=>{"use strict";t.exports=window.wp.element},7723:t=>{"use strict";t.exports=window.wp.i18n},5573:t=>{"use strict";t.exports=window.wp.primitives}},n={};function r(t){var e=n[t];if(void 0!==e)return e.exports;var o=n[t]={exports:{}};return i[t].call(o.exports,o,o.exports,r),o.exports}r.m=i,t=[],r.O=(e,o,i,n)=>{if(!o){var s=1/0;for(d=0;d<t.length;d++){for(var[o,i,n]=t[d],c=!0,a=0;a<o.length;a++)(!1&n||s>=n)&&Object.keys(r.O).every((t=>r.O[t](o[a])))?o.splice(a--,1):(c=!1,n<s&&(s=n));if(c){t.splice(d--,1);var l=i();void 0!==l&&(e=l)}}return e}n=n||0;for(var d=t.length;d>0&&t[d-1][2]>n;d--)t[d]=t[d-1];t[d]=[o,i,n]},r.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return r.d(e,{a:e}),e},o=Object.getPrototypeOf?t=>Object.getPrototypeOf(t):t=>t.__proto__,r.t=function(t,i){if(1&i&&(t=this(t)),8&i)return t;if("object"==typeof t&&t){if(4&i&&t.__esModule)return t;if(16&i&&"function"==typeof t.then)return t}var n=Object.create(null);r.r(n);var s={};e=e||[null,o({}),o([]),o(o)];for(var c=2&i&&t;"object"==typeof c&&!~e.indexOf(c);c=o(c))Object.getOwnPropertyNames(c).forEach((e=>s[e]=()=>t[e]));return s.default=()=>t,r.d(n,s),n},r.d=(t,e)=>{for(var o in e)r.o(e,o)&&!r.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.j=2100,(()=>{var t={2100:0};r.O.j=e=>0===t[e];var e=(e,o)=>{var i,n,[s,c,a]=o,l=0;if(s.some((e=>0!==t[e]))){for(i in c)r.o(c,i)&&(r.m[i]=c[i]);if(a)var d=a(r)}for(e&&e(o);l<s.length;l++)n=s[l],r.o(t,n)&&t[n]&&t[n][0](),t[n]=0;return r.O(d)},o=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];o.forEach(e.bind(null,0)),o.push=e.bind(null,o.push.bind(o))})();var s=r.O(void 0,[94],(()=>r(7755)));s=r.O(s),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["add-to-cart-form"]=s})();