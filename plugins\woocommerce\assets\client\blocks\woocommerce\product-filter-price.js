import*as e from"@wordpress/interactivity";var t={d:(e,i)=>{for(var r in i)t.o(i,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:i[r]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const i=(l={getConfig:()=>e.getConfig,getContext:()=>e.getContext,getServerContext:()=>e.getServerContext,store:()=>e.store},p={},t.d(p,l),p),r=window.wc.wcSettings.SITE_CURRENCY,n=(e={})=>({...r,...e}),a=(e,t)=>{if(""===e||void 0===e)return"";const i="number"==typeof e?e:parseInt(e,10);if(!Number.isFinite(i))return"";const r=n(t),{minorUnit:a,prefix:c,suffix:o,decimalSeparator:m,thousandSeparator:s}=r,l=i/10**a,{beforeDecimal:p,afterDecimal:v}=(e=>{const t=e.split(".");return{beforeDecimal:t[0],afterDecimal:t[1]||""}})(l.toString()),g=`${c}${((e,t)=>e.replace(/\B(?=(\d{3})+(?!\d))/g,t))(p,s)}${((e,t,i)=>e?`${t}${e.padEnd(i,"0")}`:i>0?`${t}${"0".repeat(i)}`:"")(v,m,a)}${o}`;return g},{store:c,getContext:o,getServerContext:m,getConfig:s}=i;var l,p;function v(e,t,i){return e>=t&&e<=i}const g={state:{get minPrice(){const{activeFilters:e}=o(),{minRange:t}=m?m():o(),i=e.find((e=>"price"===e.type));if(i){const[e]=i.value.split("|");return e?parseInt(e,10):t}return t},get maxPrice(){const{activeFilters:e}=o(),{maxRange:t}=m?m():o(),i=e.find((e=>"price"===e.type));if(i){const[,e]=i.value.split("|");return e?parseInt(e,10):t}return t},get formattedMinPrice(){return a(u.minPrice,n({minorUnit:0}))},get formattedMaxPrice(){return a(u.maxPrice,n({minorUnit:0}))}},actions:{getActivePriceAndLabel(e,t){const{minRange:i,maxRange:r}=m?m():o(),{activePriceLabelTemplates:c}=s();return e&&e>i&&t&&t<r?{activeValue:`${e}|${t}`,activeLabel:c.minAndMax.replace("{{min}}",a(e,n({minorUnit:0}))).replace("{{max}}",a(t,n({minorUnit:0})))}:e&&e>i?{activeValue:`${e}|`,activeLabel:c.minOnly.replace("{{min}}",a(e,n({minorUnit:0})))}:t&&t<r?{activeValue:`|${t}`,activeLabel:c.maxOnly.replace("{{max}}",a(t,n({minorUnit:0})))}:{activeValue:"",activeLabel:""}},setPrice:(e,t)=>{const i=o(),{minRange:r,maxRange:n}=m?m():o(),a={min:u.minPrice,max:u.maxPrice};"min"===e&&t&&v(t,r,n)&&t<u.maxPrice&&(a.min=t),"max"===e&&t&&v(t,r,n)&&t>u.minPrice&&(a.max=t),a.min===r&&(a.min=0),a.max===n&&(a.max=0),i.activeFilters=i.activeFilters.filter((e=>"price"!==e.type));const{activeValue:c,activeLabel:s}=x.getActivePriceAndLabel(a.min,a.max);if(c){const e={type:"price",value:c,activeLabel:s};i.activeFilters.push(e)}},setMinPrice:e=>{const t=parseInt(e.target.value,10);x.setPrice("min",t)},setMaxPrice:e=>{const t=parseInt(e.target.value,10);x.setPrice("max",t)}}},{state:u,actions:x}=c("woocommerce/product-filters",g);