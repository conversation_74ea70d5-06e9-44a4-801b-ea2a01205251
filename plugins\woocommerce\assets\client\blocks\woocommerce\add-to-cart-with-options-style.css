.wc-block-add-to-cart-with-options-grouped-product-selector-item-cta .quantity .input-text,.wc-block-add-to-cart-with-options__quantity-selector .quantity .input-text{font-size:var(--wp--preset--font-size--small);padding:.9rem 1.1rem}.wc-block-add-to-cart-with-options-grouped-product-selector-item-cta .wc-block-components-quantity-selector,.wc-block-add-to-cart-with-options__quantity-selector .wc-block-components-quantity-selector{display:inline-flex;margin-bottom:0;margin-right:.5em;width:unset}.wc-block-add-to-cart-with-options-grouped-product-selector-item-cta .wc-block-components-quantity-selector:after,.wc-block-add-to-cart-with-options__quantity-selector .wc-block-components-quantity-selector:after{border:1px solid;opacity:.3}.wc-block-add-to-cart-with-options-grouped-product-selector-item-cta .wc-block-components-quantity-selector .input-text,.wc-block-add-to-cart-with-options__quantity-selector .wc-block-components-quantity-selector .input-text{font-size:var(--wp--preset--font-size--small)}.wc-block-add-to-cart-with-options-grouped-product-selector-item-cta .wc-block-components-quantity-selector input[type=number].input-text.qty.text,.wc-block-add-to-cart-with-options__quantity-selector .wc-block-components-quantity-selector input[type=number].input-text.qty.text{-moz-appearance:textfield;background-color:transparent;border:unset;color:currentColor;font-size:var(--wp--preset--font-size--small);font-weight:600;margin:0;margin-right:unset;order:1;padding:.9rem 0;text-align:center}.wc-block-add-to-cart-with-options-grouped-product-selector-item-cta .wc-block-components-quantity-selector input[type=number].input-text.qty.text:focus,.wc-block-add-to-cart-with-options__quantity-selector .wc-block-components-quantity-selector input[type=number].input-text.qty.text:focus{border-radius:unset}.wc-block-add-to-cart-with-options-grouped-product-selector-item-cta .wc-block-components-quantity-selector input[type=number]::-webkit-inner-spin-button,.wc-block-add-to-cart-with-options-grouped-product-selector-item-cta .wc-block-components-quantity-selector input[type=number]::-webkit-outer-spin-button,.wc-block-add-to-cart-with-options__quantity-selector .wc-block-components-quantity-selector input[type=number]::-webkit-inner-spin-button,.wc-block-add-to-cart-with-options__quantity-selector .wc-block-components-quantity-selector input[type=number]::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.wc-block-add-to-cart-with-options.is-invalid .wp-block-woocommerce-product-button .add_to_cart_button{cursor:not-allowed}
