import*as e from"@wordpress/interactivity";var t={438:e=>{e.exports=import("@wordpress/interactivity-router")}},o={};function r(e){var i=o[e];if(void 0!==i)return i.exports;var n=o[e]={exports:{}};return t[e](n,n.exports,r),n.exports}r.d=(e,t)=>{for(var o in t)r.o(t,o)&&!r.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);const i=(n={getElement:()=>e.getElement,store:()=>e.store},s={},r.d(s,n),s);var n,s;(0,i.store)("woocommerce/blockified-product-reviews",{actions:{*navigate(e){e.preventDefault();const{ref:t}=(0,i.getElement)();if(!function(e){return null!==e&&e instanceof window.HTMLAnchorElement&&!!e.href&&(!e.target||"_self"===e.target)&&e.origin===window.location.origin}(t))return;const{actions:o}=yield Promise.resolve().then(r.bind(r,438));yield o.navigate(t.href),t.closest(".wp-block-woocommerce-blockified-product-reviews")?.scrollIntoView({behavior:"smooth",block:"start"})}}},{lock:!0});