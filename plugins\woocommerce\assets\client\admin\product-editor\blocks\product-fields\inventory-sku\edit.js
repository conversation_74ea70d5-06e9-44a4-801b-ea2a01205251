"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.Edit=Edit;const i18n_1=require("@wordpress/i18n"),compose_1=require("@wordpress/compose"),element_1=require("@wordpress/element"),block_templates_1=require("@woocommerce/block-templates"),components_1=require("@wordpress/components"),core_data_1=require("@wordpress/core-data"),validation_context_1=require("../../../contexts/validation-context");function Edit({attributes:e,context:o}){const t=(0,block_templates_1.useWooBlockProps)(e),[r,n]=(0,core_data_1.useEntityProp)("postType",o.postType,"sku"),{ref:s}=(0,validation_context_1.useValidation)("sku",(async function(){}),[r]),c=(0,compose_1.useInstanceId)(components_1.BaseControl,"product_sku");return(0,element_1.createElement)("div",{...t},(0,element_1.createElement)(components_1.BaseControl,{id:c,className:"woocommerce-product-form_inventory-sku",label:(0,element_1.createInterpolateElement)((0,i18n_1.__)("Sku <description />","woocommerce"),{description:(0,element_1.createElement)("span",{className:"woocommerce-product-form__optional-input"},(0,i18n_1.__)("(STOCK KEEPING UNIT)","woocommerce"))})},(0,element_1.createElement)(components_1.__experimentalInputControl,{ref:s,id:c,name:"woocommerce-product-sku",onChange:e=>{n(e??"")},value:r||"",disabled:e.disabled})))}