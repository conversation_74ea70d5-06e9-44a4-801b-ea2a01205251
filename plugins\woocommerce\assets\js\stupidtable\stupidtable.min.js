!function(t){t.fn.stupidtable=function(r){return this.each(function(){var n=t(this);r=r||{},r=t.extend({},t.fn.stupidtable.default_sort_fns,r),n.on("click.stupidtable","thead th",function(){var a=t(this),e=0,i=t.fn.stupidtable.dir;a.parents("tr").find("th").slice(0,a.index()+1).each(function(){var r=t(this).attr("colspan")||1;e+=parseInt(r,10)}),e-=1;var s=a.data("sortDefault")||i.ASC;a.data("sortDir")&&(s=a.data("sortDir")===i.ASC?i.DESC:i.ASC);var o=a.data("sort")||null;null!==o&&(n.trigger("beforetablesort",{column:a.index(),direction:s}),n.css("display"),setTimeout(function(){var c=r[o];n.children("tbody").each(function(r,n){var a=[],o=t(n),d=o.children("tr").not("[data-sort-ignore]");d.each(function(r,n){var i=t(n).children().eq(e),s=i.data("sortValue"),o=void 0!==s?s:i.text();a.push([o,n])}),a.sort(function(t,r){return c(t[0],r[0])}),s!=i.ASC&&a.reverse(),d=t.map(a,function(t){return t[1]}),o.append(d)}),n.find("th").data("sortDir",null).removeClass("sorting-desc sorting-asc"),a.data("sortDir",s).addClass("sorting-"+s),n.trigger("aftertablesort",{column:a.index(),direction:s}),n.css("display")},10))})})},t.fn.stupidtable.dir={ASC:"asc",DESC:"desc"},t.fn.stupidtable.default_sort_fns={int:function(t,r){return parseInt(t,10)-parseInt(r,10)},float:function(t,r){return parseFloat(t)-parseFloat(r)},string:function(t,r){return t.localeCompare(r)},"string-ins":function(t,r){return t=t.toLocaleLowerCase(),r=r.toLocaleLowerCase(),t.localeCompare(r)}}}(jQuery);