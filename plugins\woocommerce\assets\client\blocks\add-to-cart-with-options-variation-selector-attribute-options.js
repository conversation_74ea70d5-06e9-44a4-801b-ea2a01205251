(()=>{var t,e,o,r={6206:(t,e,o)=>{"use strict";const r=window.wp.blocks;var i=o(4530),n=o(1331);const s=JSON.parse('{"name":"woocommerce/add-to-cart-with-options-variation-selector-attribute-options","title":"Variation Selector Attribute Options (Experimental)","description":"The attribute options of a given variable product attribute.","category":"woocommerce-product-elements","keywords":["WooCommerce"],"ancestor":["woocommerce/add-to-cart-with-options-variation-selector-item"],"attributes":{"style":{"type":"string","enum":["pills","dropdown"],"default":"pills"}},"textdomain":"woocommerce","apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json","supports":{"inserter":false,"interactivity":true},"usesContext":["woocommerce/attributeId","woocommerce/attributeName","woocommerce/attributeTerms"],"viewScriptModule":"woocommerce/add-to-cart-with-options-variation-selector-attribute-options","style":"file:../woocommerce/add-to-cart-with-options-variation-selector-attribute-options-style.css"}');var a=o(7723);const c=window.wp.blockEditor,l=window.wp.components,d=window.wc.wcBlocksSharedContext;var p=o(4921),u=o(6087),w=o(790);function m({id:t,options:e}){return(0,w.jsx)("ul",{id:t,className:"wc-block-add-to-cart-with-options-variation-selector-attribute-options__pills",children:e.map(((t,e)=>(0,w.jsx)("li",{className:(0,p.A)("wc-block-add-to-cart-with-options-variation-selector-attribute-options__pill",{"wc-block-add-to-cart-with-options-variation-selector-attribute-options__pill--selected":0===e,"wc-block-add-to-cart-with-options-variation-selector-attribute-options__pill--disabled":t.disabled}),children:t.label},t.value)))})}const b=window.wc.wcSettings,v=window.wc.wcTypes,h=(0,b.getSettingWithCoercion)("isBlockifiedAddToCart",!1,v.isBoolean),f=(0,b.getSetting)("isBlockTheme"),y=h&&f;o(8180),y&&(0,r.registerBlockType)(s,{edit:function(t){const{attributes:e,setAttributes:o}=t,{className:r,style:i}=e,n=(0,c.useBlockProps)({className:r});var s,p;s="add-to-cart-with-options-variation-selector-attribute-options",p=({editorBackgroundColor:t,editorColor:e})=>`\n\t\t\t:where(.wc-block-add-to-cart-with-options-variation-selector-attribute-options__pill--selected) {\n\t\t\t\tbackground-color: ${e};\n\t\t\t\tcolor: ${t};\n\t\t\t\tborder-color: ${e};\n\t\t\t}\n\t\t`,(0,u.useEffect)((()=>{let t=document.querySelector(".editor-styles-wrapper");if(!t){const e=document.querySelector(".edit-site-visual-editor__editor-canvas");if(!(e&&e instanceof HTMLIFrameElement))return;const o=e.contentDocument||e.contentWindow?.document;if(!o)return;t=o.querySelector(".editor-styles-wrapper")}if(!t)return;const e=window.getComputedStyle(t),o=e?.backgroundColor,r=e?.color;if(!o||!r)return;const i=`${s}-editor-theme-colors`;if(t.querySelector(`#${i}`))return;const n=p({editorBackgroundColor:o,editorColor:r}),a=document.createElement("style");a.id=i,a.appendChild(document.createTextNode(n)),t.appendChild(a)}),[p,s]);const{data:b}=(0,d.useCustomDataContext)("attribute");if(!b)return;const v=b.terms.map(((t,e)=>({value:t.slug,label:t.name,disabled:e>1&&e===b.terms.length-1})));return(0,w.jsxs)("div",{...n,children:[(0,w.jsx)(c.InspectorControls,{children:(0,w.jsx)(l.PanelBody,{title:(0,a.__)("Style","woocommerce"),children:(0,w.jsxs)(l.__experimentalToggleGroupControl,{value:i,onChange:t=>{o({style:t})},isBlock:!0,hideLabelFromVision:!0,size:"__unstable-large",children:[(0,w.jsx)(l.__experimentalToggleGroupControlOption,{value:"pills",label:(0,a.__)("Pills","woocommerce")}),(0,w.jsx)(l.__experimentalToggleGroupControlOption,{value:"dropdown",label:(0,a.__)("Dropdown","woocommerce")})]})})}),(0,w.jsx)(l.Disabled,{children:"pills"===i?(0,w.jsx)(m,{id:b.taxonomy,options:v}):(0,w.jsx)("select",{id:b.taxonomy,className:"wc-block-add-to-cart-with-options-variation-selector-attribute-options__dropdown",children:v.map((t=>(0,w.jsx)("option",{value:t.value,children:t.label},t.value)))})})]})},attributes:s.attributes,icon:{src:(0,w.jsx)(i.A,{icon:n.A})},save:()=>null})},8180:()=>{},1609:t=>{"use strict";t.exports=window.React},790:t=>{"use strict";t.exports=window.ReactJSXRuntime},6087:t=>{"use strict";t.exports=window.wp.element},7723:t=>{"use strict";t.exports=window.wp.i18n},5573:t=>{"use strict";t.exports=window.wp.primitives}},i={};function n(t){var e=i[t];if(void 0!==e)return e.exports;var o=i[t]={exports:{}};return r[t].call(o.exports,o,o.exports,n),o.exports}n.m=r,t=[],n.O=(e,o,r,i)=>{if(!o){var s=1/0;for(d=0;d<t.length;d++){for(var[o,r,i]=t[d],a=!0,c=0;c<o.length;c++)(!1&i||s>=i)&&Object.keys(n.O).every((t=>n.O[t](o[c])))?o.splice(c--,1):(a=!1,i<s&&(s=i));if(a){t.splice(d--,1);var l=r();void 0!==l&&(e=l)}}return e}i=i||0;for(var d=t.length;d>0&&t[d-1][2]>i;d--)t[d]=t[d-1];t[d]=[o,r,i]},n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e},o=Object.getPrototypeOf?t=>Object.getPrototypeOf(t):t=>t.__proto__,n.t=function(t,r){if(1&r&&(t=this(t)),8&r)return t;if("object"==typeof t&&t){if(4&r&&t.__esModule)return t;if(16&r&&"function"==typeof t.then)return t}var i=Object.create(null);n.r(i);var s={};e=e||[null,o({}),o([]),o(o)];for(var a=2&r&&t;"object"==typeof a&&!~e.indexOf(a);a=o(a))Object.getOwnPropertyNames(a).forEach((e=>s[e]=()=>t[e]));return s.default=()=>t,n.d(i,s),i},n.d=(t,e)=>{for(var o in e)n.o(e,o)&&!n.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.j=277,(()=>{var t={277:0};n.O.j=e=>0===t[e];var e=(e,o)=>{var r,i,[s,a,c]=o,l=0;if(s.some((e=>0!==t[e]))){for(r in a)n.o(a,r)&&(n.m[r]=a[r]);if(c)var d=c(n)}for(e&&e(o);l<s.length;l++)i=s[l],n.o(t,i)&&t[i]&&t[i][0](),t[i]=0;return n.O(d)},o=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];o.forEach(e.bind(null,0)),o.push=e.bind(null,o.push.bind(o))})();var s=n.O(void 0,[94],(()=>n(6206)));s=n.O(s),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["add-to-cart-with-options-variation-selector-attribute-options"]=s})();