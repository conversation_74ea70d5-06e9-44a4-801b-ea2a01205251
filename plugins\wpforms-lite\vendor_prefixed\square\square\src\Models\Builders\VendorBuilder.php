<?php

declare (strict_types=1);
namespace WPForms\Vendor\Square\Models\Builders;

use WP<PERSON>orms\Vendor\Core\Utils\CoreHelper;
use WPF<PERSON>\Vendor\Square\Models\Address;
use WPF<PERSON>\Vendor\Square\Models\Vendor;
use WPF<PERSON>\Vendor\Square\Models\VendorContact;
/**
 * Builder for model Vendor
 *
 * @see Vendor
 */
class VendorBuilder
{
    /**
     * @var Vendor
     */
    private $instance;
    private function __construct(Vendor $instance)
    {
        $this->instance = $instance;
    }
    /**
     * Initializes a new Vendor Builder object.
     */
    public static function init() : self
    {
        return new self(new Vendor());
    }
    /**
     * Sets id field.
     *
     * @param string|null $value
     */
    public function id(?string $value) : self
    {
        $this->instance->setId($value);
        return $this;
    }
    /**
     * Sets created at field.
     *
     * @param string|null $value
     */
    public function createdAt(?string $value) : self
    {
        $this->instance->setCreatedAt($value);
        return $this;
    }
    /**
     * Sets updated at field.
     *
     * @param string|null $value
     */
    public function updatedAt(?string $value) : self
    {
        $this->instance->setUpdatedAt($value);
        return $this;
    }
    /**
     * Sets name field.
     *
     * @param string|null $value
     */
    public function name(?string $value) : self
    {
        $this->instance->setName($value);
        return $this;
    }
    /**
     * Unsets name field.
     */
    public function unsetName() : self
    {
        $this->instance->unsetName();
        return $this;
    }
    /**
     * Sets address field.
     *
     * @param Address|null $value
     */
    public function address(?Address $value) : self
    {
        $this->instance->setAddress($value);
        return $this;
    }
    /**
     * Sets contacts field.
     *
     * @param VendorContact[]|null $value
     */
    public function contacts(?array $value) : self
    {
        $this->instance->setContacts($value);
        return $this;
    }
    /**
     * Unsets contacts field.
     */
    public function unsetContacts() : self
    {
        $this->instance->unsetContacts();
        return $this;
    }
    /**
     * Sets account number field.
     *
     * @param string|null $value
     */
    public function accountNumber(?string $value) : self
    {
        $this->instance->setAccountNumber($value);
        return $this;
    }
    /**
     * Unsets account number field.
     */
    public function unsetAccountNumber() : self
    {
        $this->instance->unsetAccountNumber();
        return $this;
    }
    /**
     * Sets note field.
     *
     * @param string|null $value
     */
    public function note(?string $value) : self
    {
        $this->instance->setNote($value);
        return $this;
    }
    /**
     * Unsets note field.
     */
    public function unsetNote() : self
    {
        $this->instance->unsetNote();
        return $this;
    }
    /**
     * Sets version field.
     *
     * @param int|null $value
     */
    public function version(?int $value) : self
    {
        $this->instance->setVersion($value);
        return $this;
    }
    /**
     * Sets status field.
     *
     * @param string|null $value
     */
    public function status(?string $value) : self
    {
        $this->instance->setStatus($value);
        return $this;
    }
    /**
     * Initializes a new Vendor object.
     */
    public function build() : Vendor
    {
        return CoreHelper::clone($this->instance);
    }
}
