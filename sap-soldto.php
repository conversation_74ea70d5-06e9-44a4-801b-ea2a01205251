<?php
// DEBUG: catch any fatal errors and log them
add_action( 'init', function() {
    register_shutdown_function( function() {
        $err = error_get_last();
        if ( $err && in_array( $err['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR] ) ) {
            error_log( "🔥 Fatal error in SAP-SoldTo API: " . print_r( $err, true ) );
            error_log( print_r( debug_backtrace( DEBUG_BACKTRACE_IGNORE_ARGS ), true ) );
        }
    });
});

// DEBUG: intercept wp_die() and log a trace
add_filter( 'wp_die_handler', function() {
    return function( $message, $title = '', $args = [] ) {
        error_log( "⚠️ wp_die fired: {$message}" );
        error_log( print_r( debug_backtrace( DEBUG_BACKTRACE_IGNORE_ARGS ), true ) );
        // then call the normal handler so WP will still die
        _default_wp_die_handler( $message, $title, $args );
    };
});

/**
 * Plugin Name: SAP Sold TO API
 * Description: Creates WP users from JSON and adds all SoldTo/billing fields as user meta.
 * Version:     1.4
 * Author:      ATAK Interactive
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Register REST route
add_action( 'rest_api_init', function() {
    register_rest_route( 'wc/v3', '/sap-soldto', [
        'methods'             => 'POST',
        'callback'            => 'cuc_create_customer_endpoint',
        'permission_callback' => function() {
            // Allow if user can create users OR if it's an API request with proper authentication
            return current_user_can( 'create_users' ) || current_user_can( 'manage_woocommerce' );
        },
    ] );
} );

/**
 * Endpoint logic: create user, then add ALL meta after clearing caches.
 */
function cuc_create_customer_endpoint( WP_REST_Request $request ) {
    $data = $request->get_json_params();

    // Log the incoming data for debugging
    error_log( "🔍 SAP-SoldTo API received data: " . print_r( $data, true ) );

    // — Enhanced Validation —
    if ( empty( $data ) || ! is_array( $data ) ) {
        error_log( "❌ No valid JSON data received" );
        return new WP_Error( 'invalid_data', 'No valid JSON data received', [ 'status'=>400 ] );
    }

    if ( empty( $data['soldTo'] ) || ! is_array( $data['soldTo'] ) ) {
        error_log( "❌ soldTo data is missing or invalid" );
        return new WP_Error( 'missing_soldto', 'soldTo data is required and must be an object', [ 'status'=>400 ] );
    }

    if ( empty( $data['soldTo']['customerId'] ) ) {
        error_log( "❌ customerId is missing" );
        return new WP_Error( 'missing_customer_id', 'soldTo.customerId is required', [ 'status'=>400 ] );
    }
    if ( empty( $data['soldTo']['email'] ) || ! is_email( $data['soldTo']['email'] ) ) {
        error_log( "❌ Invalid email: " . ( $data['soldTo']['email'] ?? 'empty' ) );
        return new WP_Error( 'invalid_email', 'A valid soldTo.email is required', [ 'status'=>400 ] );
    }

    // — Create user —
    $email    = sanitize_email( $data['soldTo']['email'] );
    $username = sanitize_user( $email, true );

    error_log( "🔍 Attempting to create user with email: {$email}, username: {$username}" );

    if ( username_exists( $username ) || email_exists( $email ) ) {
        error_log( "❌ User already exists: {$email}" );
        return new WP_Error( 'user_exists', 'User/email already exists', [ 'status'=>400 ] );
    }

    $password = wp_generate_password( 12, false );
    $user_id  = wp_insert_user([
        'user_login' => $username,
        'user_pass'  => $password,
        'user_email'=> $email,
        'role'       => 'customer',
    ]);

    if ( is_wp_error( $user_id ) ) {
        error_log( "❌ Failed to create user: " . $user_id->get_error_message() );
        return $user_id;
    }

    error_log( "✅ User created successfully with ID: {$user_id}" );

    // — Ensure WooCommerce customer setup —
    if ( class_exists( 'WC_Customer' ) ) {
        try {
            $customer = new WC_Customer( $user_id );
            if ( $customer->get_id() ) {
                error_log( "✅ WooCommerce customer object created for user {$user_id}" );
            }
        } catch ( Exception $e ) {
            error_log( "⚠️ WooCommerce customer creation warning: " . $e->getMessage() );
        }
    }

    // — Clear caches so meta writes hit the DB —
    if ( function_exists('clean_user_cache') ) {
        clean_user_cache( $user_id );
    }
    if ( function_exists('wp_cache_flush') ) {
        wp_cache_flush();
    }

    // Additional cache clearing for user meta
    wp_cache_delete( $user_id, 'user_meta' );
    wp_cache_delete( $user_id, 'users' );

    // Force a small delay to ensure database writes complete
    usleep( 100000 ); // 0.1 seconds

    // — Add SoldTo meta —
    $sold = $data['soldTo'];
    error_log( "🔍 Adding SoldTo metadata for user {$user_id}" );
    error_log( "🔍 SoldTo data: " . print_r( $sold, true ) );

    // Add customer_id (most critical field)
    $customer_id = sanitize_text_field( $sold['customerId'] );
    error_log( "🔍 Attempting to add customer_id: {$customer_id}" );
    $result = add_user_meta( $user_id, 'customer_id', $customer_id );
    if ( $result === false ) {
        error_log( "❌ Failed to add customer_id, trying update..." );
        $result = update_user_meta( $user_id, 'customer_id', $customer_id );
        error_log( "🔄 Update customer_id result: " . var_export($result, true) );
    } else {
        error_log( "✅ Added customer_id successfully: " . var_export($result, true) );
    }

    // Add other SoldTo fields
    $soldto_fields = [
        'company_code' => $sold['companyCode'] ?? '',
        'country_code' => $sold['countryCode'] ?? '',
        'price_group'  => $sold['priceGroup'] ?? ''
    ];

    foreach ( $soldto_fields as $meta_key => $meta_value ) {
        if ( !empty( $meta_value ) ) {
            $sanitized_value = sanitize_text_field( $meta_value );
            $result = add_user_meta( $user_id, $meta_key, $sanitized_value );
            if ( $result === false ) {
                $result = update_user_meta( $user_id, $meta_key, $sanitized_value );
                error_log( "🔄 Updated {$meta_key}: " . var_export($result, true) );
            } else {
                error_log( "✅ Added {$meta_key}: " . var_export($result, true) );
            }
        }
    }

    // — Add billingAddress meta —
    error_log( "🔍 Adding billing address metadata for user {$user_id}" );
    $bill = is_array( $data['billingAddress'] ) ? $data['billingAddress'] : [];
    error_log( "🔍 Billing data: " . print_r( $bill, true ) );

    // Add billing company
    if ( !empty( $bill['company'] ) ) {
        $company = sanitize_text_field( $bill['company'] );
        $result = add_user_meta( $user_id, 'billing_company', $company );
        if ( $result === false ) {
            $result = update_user_meta( $user_id, 'billing_company', $company );
            error_log( "🔄 Updated billing_company: " . var_export($result, true) );
        } else {
            error_log( "✅ Added billing_company: " . var_export($result, true) );
        }
    }

    // Add billing address fields
    $addr = is_array( $bill['address'] ) ? $bill['address'] : [];
    $billing_fields = [
      'billing_address_1' => $addr['line1']         ?? '',
      'billing_address_2' => $addr['line2']         ?? '',
      'billing_city'      => $addr['city']          ?? '',
      'billing_postcode'  => $addr['postcode']      ?? '',
      'billing_country'   => $addr['countryRegion'] ?? '',
      'billing_state'     => $addr['stateCounty']   ?? '',
    ];

    foreach ( $billing_fields as $meta_key => $meta_val ) {
        if ( !empty( $meta_val ) ) {
            $sanitized_value = sanitize_text_field( $meta_val );
            $result = add_user_meta( $user_id, $meta_key, $sanitized_value );
            if ( $result === false ) {
                $result = update_user_meta( $user_id, $meta_key, $sanitized_value );
                error_log( "🔄 Updated {$meta_key}: " . var_export($result, true) );
            } else {
                error_log( "✅ Added {$meta_key}: " . var_export($result, true) );
            }
        }
    }

    // — Add shiptos array —
    error_log( "🔍 Adding shiptos metadata for user {$user_id}" );
    $shiptos = is_array( $data['shiptos'] ) ? $data['shiptos'] : [];
    error_log( "🔍 Shiptos data: " . print_r( $shiptos, true ) );

    if ( !empty( $shiptos ) ) {
        $sanitized_shiptos = array_map( 'sanitize_text_field', $shiptos );
        $result = add_user_meta( $user_id, 'shiptos', $sanitized_shiptos );
        if ( $result === false ) {
            $result = update_user_meta( $user_id, 'shiptos', $sanitized_shiptos );
            error_log( "🔄 Updated shiptos: " . var_export($result, true) );
        } else {
            error_log( "✅ Added shiptos: " . var_export($result, true) );
        }
    }

    // — Verify metadata was saved —
    error_log( "🔍 Verifying metadata was saved for user {$user_id}" );
    $saved_customer_id = get_user_meta( $user_id, 'customer_id', true );
    $saved_company_code = get_user_meta( $user_id, 'company_code', true );

    if ( empty( $saved_customer_id ) ) {
        error_log( "❌ CRITICAL: customer_id was not saved for user {$user_id}" );
    } else {
        error_log( "✅ Verified customer_id saved: {$saved_customer_id}" );
    }

    // — Response —
    return rest_ensure_response([
        'success'  => true,
        'user_id'  => $user_id,
        'username' => $username,
        'password' => $password,
        'metadata_verification' => [
            'customer_id' => $saved_customer_id,
            'company_code' => $saved_company_code,
        ]
    ]);
}
