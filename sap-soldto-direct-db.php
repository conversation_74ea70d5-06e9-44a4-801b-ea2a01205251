<?php
/**
 * Plugin Name: SAP Sold TO API - Direct DB Version
 * Description: Creates users via direct database insert to bypass security restrictions
 * Version:     1.6-DIRECT-DB
 * Author:      ATAK Interactive
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Register REST route
add_action( 'rest_api_init', function() {
    register_rest_route( 'wc/v3', '/sap-soldto-direct', [
        'methods'             => 'POST',
        'callback'            => 'cuc_create_customer_direct_db',
        'permission_callback' => '__return_true',
    ] );
} );

/**
 * Create user via direct database insert
 */
function cuc_create_customer_direct_db( WP_REST_Request $request ) {
    global $wpdb;
    
    error_log( "🚀 SAP-SoldTo DIRECT DB API called" );
    
    $data = $request->get_json_params();
    error_log( "🔍 Received data: " . print_r( $data, true ) );

    // Validation
    if ( empty( $data['soldTo']['customerId'] ) || empty( $data['soldTo']['email'] ) ) {
        return new WP_Error( 'missing_data', 'customerId and email are required', [ 'status' => 400 ] );
    }

    $email = sanitize_email( $data['soldTo']['email'] );
    $customer_id = sanitize_text_field( $data['soldTo']['customerId'] );
    
    // Check if user exists
    $existing_user = $wpdb->get_var( $wpdb->prepare(
        "SELECT ID FROM {$wpdb->users} WHERE user_email = %s OR user_login = %s",
        $email, $email
    ) );
    
    if ( $existing_user ) {
        error_log( "❌ User already exists with ID: {$existing_user}" );
        return new WP_Error( 'user_exists', 'User already exists', [ 'status' => 400 ] );
    }

    // Generate password
    $password = wp_generate_password( 12, false );
    $password_hash = wp_hash_password( $password );
    
    // Insert user directly into database
    $user_data = [
        'user_login'          => $email,
        'user_pass'           => $password_hash,
        'user_nicename'       => sanitize_title( $email ),
        'user_email'          => $email,
        'user_registered'     => current_time( 'mysql' ),
        'user_activation_key' => '',
        'user_status'         => 0,
        'display_name'        => $email
    ];
    
    error_log( "🔍 Inserting user directly into database" );
    
    $result = $wpdb->insert( $wpdb->users, $user_data );
    
    if ( $result === false ) {
        error_log( "❌ Direct database insert failed: " . $wpdb->last_error );
        return new WP_Error( 'db_insert_failed', 'Database insert failed: ' . $wpdb->last_error, [ 'status' => 500 ] );
    }
    
    $user_id = $wpdb->insert_id;
    error_log( "✅ User created via direct DB insert with ID: {$user_id}" );
    
    // Add user role
    $wpdb->insert( $wpdb->usermeta, [
        'user_id'    => $user_id,
        'meta_key'   => $wpdb->prefix . 'capabilities',
        'meta_value' => serialize( [ 'customer' => true ] )
    ] );
    
    $wpdb->insert( $wpdb->usermeta, [
        'user_id'    => $user_id,
        'meta_key'   => $wpdb->prefix . 'user_level',
        'meta_value' => '0'
    ] );
    
    // Add custom metadata
    $meta_data = [
        'customer_id'  => $customer_id,
        'company_code' => $data['soldTo']['companyCode'] ?? '',
        'country_code' => $data['soldTo']['countryCode'] ?? '',
        'price_group'  => $data['soldTo']['priceGroup'] ?? '',
    ];
    
    // Add billing address if provided
    if ( !empty( $data['billingAddress'] ) ) {
        $bill = $data['billingAddress'];
        $addr = $bill['address'] ?? [];
        
        $meta_data['billing_company'] = $bill['company'] ?? '';
        $meta_data['billing_address_1'] = $addr['line1'] ?? '';
        $meta_data['billing_address_2'] = $addr['line2'] ?? '';
        $meta_data['billing_city'] = $addr['city'] ?? '';
        $meta_data['billing_postcode'] = $addr['postcode'] ?? '';
        $meta_data['billing_country'] = $addr['countryRegion'] ?? '';
        $meta_data['billing_state'] = $addr['stateCounty'] ?? '';
    }
    
    // Add shiptos
    if ( !empty( $data['shiptos'] ) && is_array( $data['shiptos'] ) ) {
        $meta_data['shiptos'] = serialize( array_map( 'sanitize_text_field', $data['shiptos'] ) );
    }

    // Insert all metadata
    foreach ( $meta_data as $meta_key => $meta_value ) {
        if ( !empty( $meta_value ) ) {
            // Don't sanitize serialized data
            $final_value = ( $meta_key === 'shiptos' ) ? $meta_value : sanitize_text_field( $meta_value );

            $meta_result = $wpdb->insert( $wpdb->usermeta, [
                'user_id'    => $user_id,
                'meta_key'   => $meta_key,
                'meta_value' => $final_value
            ] );

            if ( $meta_result ) {
                error_log( "✅ Added metadata {$meta_key}: " . substr( $final_value, 0, 100 ) . ( strlen( $final_value ) > 100 ? '...' : '' ) );
            } else {
                error_log( "❌ Failed to add metadata {$meta_key}: " . $wpdb->last_error );
            }
        }
    }
    
    // Verify metadata was saved
    $saved_customer_id = $wpdb->get_var( $wpdb->prepare(
        "SELECT meta_value FROM {$wpdb->usermeta} WHERE user_id = %d AND meta_key = 'customer_id'",
        $user_id
    ) );
    
    error_log( "🔍 Verification - saved customer_id: " . $saved_customer_id );
    
    return rest_ensure_response([
        'success'  => true,
        'user_id'  => $user_id,
        'username' => $email,
        'password' => $password,
        'method'   => 'direct_database_insert',
        'metadata_verification' => [
            'customer_id' => $saved_customer_id,
        ]
    ]);
}
?>
