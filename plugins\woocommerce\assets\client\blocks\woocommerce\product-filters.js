import*as e from"@wordpress/interactivity";var t={438:e=>{e.exports=import("@wordpress/interactivity-router")}},r={};function i(e){var a=r[e];if(void 0!==a)return a.exports;var o=r[e]={exports:{}};return t[e](o,o.exports,i),o.exports}i.d=(e,t)=>{for(var r in t)i.o(t,r)&&!i.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);const a=(l={getContext:()=>e.getContext,getServerContext:()=>e.getServerContext,store:()=>e.store},p={},i.d(p,l),p),{getContext:o,store:n,getServerContext:s}=a,c=window.wc.wcSettings.getSetting;var l,p;const v={state:{get params(){const{activeFilters:e}=o(),t={};function r(e,r){if(e in t&&t[e].length>0)return t[e]=`${t[e]},${r}`;t[e]=r}return e.forEach((e=>{const{type:i,value:a}=e;if(a){if("price"===i){const[e,r]=a.split("|");e&&(t.min_price=e),r&&(t.max_price=r)}if("status"===i&&r("filter_stock_status",a),"rating"===i&&r("rating_filter",a),i.includes("attribute")){const[,o]=i.split("/");r(`filter_${o}`,a),t[`query_type_${o}`]=e.attributeQueryType||"or"}}})),t},get activeFilters(){const{activeFilters:e}=o();return e.filter((e=>!!e.value)).sort(((e,t)=>e.activeLabel.toLowerCase().localeCompare(t.activeLabel.toLowerCase()))).map((e=>({...e,uid:`${e.type}/${e.value}`})))},get isFilterSelected(){const{activeFilters:e,item:t}=o();return e.some((e=>e.type===t.type&&e.value===t.value))}},actions:{openOverlay:()=>{if(o().isOverlayOpened=!0,document.getElementById("wpadminbar")){const e=(document.documentElement||document.body.parentNode||document.body).scrollTop;document.body.style.setProperty("--adminbar-mobile-padding",`max(calc(var(--wp-admin--admin-bar--height) - ${e}px), 0px)`)}},closeOverlay:()=>{o().isOverlayOpened=!1},closeOverlayOnEscape:e=>{o().isOverlayOpened&&"Escape"===e.key&&d.closeOverlay()},removeActiveFiltersBy:e=>{const t=o();t.activeFilters=t.activeFilters.filter((t=>!e(t)))},toggleFilter:()=>{u.isFilterSelected?function(){const{item:e}=o();d.removeActiveFiltersBy((t=>t.type===e.type&&t.value===e.value))}():function(){const e=o(),t={value:e.item.value,type:e.item.type,attributeQueryType:e.item.attributeQueryType,activeLabel:e.activeLabelTemplate.replace("{{label}}",e.item.ariaLabel)},r=e.activeFilters.filter((e=>!(e.value===t.value&&e.type===t.type)));r.push(t),e.activeFilters=r}(),d.navigate()},*navigate(){const e=s?s():o(),t=c("canonicalUrl"),r=new URL(t),{searchParams:a}=r;for(const t in e.params)a.delete(t);for(const e in u.params)a.set(e,u.params[e]);if(window.location.href===r.href)return;const n=c("isBlockTheme"),l=c("isProductArchive");if(c("needsRefreshForInteractivityAPI",!1)||!n&&l)return window.location.href=r.href;const p=yield Promise.resolve().then(i.bind(i,438));yield p.actions.navigate(r.href)}},callbacks:{scrollLimit:()=>{const{isOverlayOpened:e}=o();document.body.style.overflow=e?"hidden":"auto"}}},{state:u,actions:d}=n("woocommerce/product-filters",v);