import*as t from"@wordpress/interactivity";var e={64:t=>{t.exports=import("@woocommerce/stores/woocommerce/cart")}},o={};function a(t){var n=o[t];if(void 0!==n)return n.exports;var i=o[t]={exports:{}};return e[t](i,i.exports,a),i.exports}a.d=(t,e)=>{for(var o in e)a.o(e,o)&&!a.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},a.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);const n=(r={getContext:()=>t.getContext,store:()=>t.store,useLayoutEffect:()=>t.useLayoutEffect},s={},a.d(s,r),s),i="I acknowledge that using a private store means my plugin will inevitably break on the next store release.";var r,s,c=function(t){return t.IDLE="IDLE",t.SLIDE_OUT="SLIDE-OUT",t.SLIDE_IN="SLIDE-IN",t}(c||{});const u=()=>(0,n.getContext)(),{state:m}=(0,n.store)("woocommerce",{},{lock:i}),{state:d}=(0,n.store)("woocommerce/product-button",{state:{get quantity(){const{productId:t}=u(),e=m.cart?.items.find((e=>e.id===t));return e?.quantity||0},get slideInAnimation(){const{animationStatus:t}=u();return t===c.SLIDE_IN},get slideOutAnimation(){const{animationStatus:t}=u();return t===c.SLIDE_OUT},get addToCartText(){const{animationStatus:t,tempQuantity:e,addToCartText:o}=u(),a=t===c.IDLE||t===c.SLIDE_OUT?e||0:d.quantity;return 0===a?o:d.inTheCartText.replace("###",a.toString())},get displayViewCart(){const{displayViewCart:t}=u();return!!t&&d.quantity>0}},actions:{*addCartItem(){const t=u(),{productId:e,quantityToAdd:o}=t;yield Promise.resolve().then(a.bind(a,64));const{actions:r}=(0,n.store)("woocommerce",{},{lock:i});yield r.addCartItem({id:e,quantity:d.quantity+o}),t.displayViewCart=!0},*refreshCartItems(){yield Promise.resolve().then(a.bind(a,64));const{actions:t}=(0,n.store)("woocommerce",{},{lock:i});t.refreshCartItems()},handleAnimationEnd(t){const e=u();"slideOut"===t.animationName?e.animationStatus=c.SLIDE_IN:"slideIn"===t.animationName&&(e.tempQuantity=d.quantity,e.animationStatus=c.IDLE)}},callbacks:{syncTempQuantityOnLoad(){const t=u();(0,n.useLayoutEffect)((()=>{t.tempQuantity=d.quantity}),[])},startAnimation(){const t=u();t.tempQuantity!==d.quantity&&t.animationStatus===c.IDLE&&(t.animationStatus=c.SLIDE_OUT)}}},{lock:!0});