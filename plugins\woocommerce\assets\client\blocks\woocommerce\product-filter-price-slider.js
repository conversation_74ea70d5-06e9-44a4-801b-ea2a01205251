import*as e from"@wordpress/interactivity";var t={d:(e,n)=>{for(var o in n)t.o(n,o)&&!t.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:n[o]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const n=(s={getContext:()=>e.getContext,getElement:()=>e.getElement,getServerContext:()=>e.getServerContext,store:()=>e.store,withScope:()=>e.withScope},g={},t.d(g,s),g),{store:o,getContext:r,getElement:i,withScope:c,getServerContext:a}=n;var s,g;function l(e,t=300){let n;return function(...o){n&&clearTimeout(n),n=setTimeout(c((()=>{e.apply(this,o)})),t)}}const m={state:{rangeStyle:()=>{const{minRange:e,maxRange:t}=a?a():r();return`--low: ${100*(p.minPrice-e)/(t-e)}%; --high: ${100*(p.maxPrice-e)/(t-e)}%;`}},actions:{selectInputContent:()=>{const e=i();e?.ref instanceof HTMLInputElement&&e.ref.select()},debounceSetMinPrice:l((e=>{u.setMinPrice(e),u.navigate()}),1e3),debounceSetMaxPrice:l((e=>{u.setMaxPrice(e),u.navigate()}),1e3)}},{state:p,actions:u}=o("woocommerce/product-filters",m);