(()=>{"use strict";var e,t,o,r={2747:(e,t,o)=>{const r=window.wp.blocks;var i=o(4530),a=o(8992);const n=JSON.parse('{"name":"woocommerce/add-to-cart-with-options-variation-selector-attribute-name","title":"Variation Selector Attribute Name (Experimental)","description":"The name of a given variable product attribute.","category":"woocommerce-product-elements","keywords":["WooCommerce"],"ancestor":["woocommerce/add-to-cart-with-options-variation-selector-item"],"textdomain":"woocommerce","apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json","supports":{"inserter":false,"interactivity":true,"align":false,"alignWide":false,"color":{"__experimentalSkipSerialization":true,"gradients":true,"__experimentalDefaultControls":{"background":true,"text":true}},"typography":{"__experimentalSkipSerialization":["fontSize","lineHeight","fontFamily","fontWeight","fontStyle","textTransform","textDecoration","letterSpacing"],"fontSize":true,"lineHeight":true,"__experimentalFontFamily":true,"__experimentalFontWeight":true,"__experimentalFontStyle":true,"__experimentalTextTransform":true,"__experimentalTextDecoration":true,"__experimentalLetterSpacing":true,"__experimentalWritingMode":true,"__experimentalDefaultControls":{"fontSize":true}},"spacing":{"__experimentalSkipSerialization":true,"padding":["horizontal","vertical"],"__experimentalDefaultControls":{"padding":true}}},"usesContext":["woocommerce/attributeId","woocommerce/attributeName","woocommerce/attributeTerms"]}'),l=window.wp.blockEditor,s=window.wc.wcBlocksSharedContext;var c=o(4921),p=o(790);const u=window.wc.wcSettings,m=window.wc.wcTypes,d=(0,u.getSettingWithCoercion)("isBlockifiedAddToCart",!1,m.isBoolean),f=(0,u.getSetting)("isBlockTheme");d&&f&&(0,r.registerBlockType)(n,{edit:function(e){const{attributes:t}=e,{className:o}=t,r=(0,l.__experimentalUseColorProps)(t),[i,a]=(0,l.useSettings)("typography.fluid","layout"),n=(0,l.getTypographyClassesAndStyles)(t,{typography:{fluid:i},layout:{wideSize:a?.wideSize}}),u=(0,l.__experimentalGetSpacingClassesAndStyles)(t),m=(0,l.useBlockProps)({className:(0,c.A)(o,r.className,n.className,u.className),style:{...r.stye,...n.style,...u.style}}),{data:d}=(0,s.useCustomDataContext)("attribute");if(d)return(0,p.jsx)("label",{...m,htmlFor:d.taxonomy,children:d.name})},attributes:n.attributes,icon:{src:(0,p.jsx)(i.A,{icon:a.A})},save:()=>null})},1609:e=>{e.exports=window.React},790:e=>{e.exports=window.ReactJSXRuntime},6087:e=>{e.exports=window.wp.element},5573:e=>{e.exports=window.wp.primitives}},i={};function a(e){var t=i[e];if(void 0!==t)return t.exports;var o=i[e]={exports:{}};return r[e].call(o.exports,o,o.exports,a),o.exports}a.m=r,e=[],a.O=(t,o,r,i)=>{if(!o){var n=1/0;for(p=0;p<e.length;p++){for(var[o,r,i]=e[p],l=!0,s=0;s<o.length;s++)(!1&i||n>=i)&&Object.keys(a.O).every((e=>a.O[e](o[s])))?o.splice(s--,1):(l=!1,i<n&&(n=i));if(l){e.splice(p--,1);var c=r();void 0!==c&&(t=c)}}return t}i=i||0;for(var p=e.length;p>0&&e[p-1][2]>i;p--)e[p]=e[p-1];e[p]=[o,r,i]},a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},o=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,a.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var i=Object.create(null);a.r(i);var n={};t=t||[null,o({}),o([]),o(o)];for(var l=2&r&&e;"object"==typeof l&&!~t.indexOf(l);l=o(l))Object.getOwnPropertyNames(l).forEach((t=>n[t]=()=>e[t]));return n.default=()=>e,a.d(i,n),i},a.d=(e,t)=>{for(var o in t)a.o(t,o)&&!a.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),a.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.j=2344,(()=>{var e={2344:0};a.O.j=t=>0===e[t];var t=(t,o)=>{var r,i,[n,l,s]=o,c=0;if(n.some((t=>0!==e[t]))){for(r in l)a.o(l,r)&&(a.m[r]=l[r]);if(s)var p=s(a)}for(t&&t(o);c<n.length;c++)i=n[c],a.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return a.O(p)},o=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))})();var n=a.O(void 0,[94],(()=>a(2747)));n=a.O(n),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["add-to-cart-with-options-variation-selector-attribute-name"]=n})();