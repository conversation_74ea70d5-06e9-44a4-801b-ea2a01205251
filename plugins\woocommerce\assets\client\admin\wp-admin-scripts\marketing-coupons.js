/*! For license information please see marketing-coupons.js.LICENSE.txt */
(()=>{var e={13240:function(e){e.exports=function(){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,n){return t=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},t(e,n)}function n(e,o,r){return n=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>,[],(function(){}))),!0}catch(e){return!1}}()?Reflect.construct:function(e,n,o){var r=[null];r.push.apply(r,n);var i=new(Function.bind.apply(e,r));return o&&t(i,o.prototype),i},n.apply(null,arguments)}function o(e){return function(e){if(Array.isArray(e))return r(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return r(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var i=Object.hasOwnProperty,a=Object.setPrototypeOf,s=Object.isFrozen,c=Object.getPrototypeOf,l=Object.getOwnPropertyDescriptor,m=Object.freeze,d=Object.seal,u=Object.create,p="undefined"!=typeof Reflect&&Reflect,f=p.apply,h=p.construct;f||(f=function(e,t,n){return e.apply(t,n)}),m||(m=function(e){return e}),d||(d=function(e){return e}),h||(h=function(e,t){return n(e,o(t))});var g,w=T(Array.prototype.forEach),v=T(Array.prototype.pop),x=T(Array.prototype.push),C=T(String.prototype.toLowerCase),E=T(String.prototype.toString),y=T(String.prototype.match),_=T(String.prototype.replace),b=T(String.prototype.indexOf),N=T(String.prototype.trim),S=T(RegExp.prototype.test),k=(g=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return h(g,t)});function T(e){return function(t){for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];return f(e,t,o)}}function j(e,t,n){var o;n=null!==(o=n)&&void 0!==o?o:C,a&&a(e,null);for(var r=t.length;r--;){var i=t[r];if("string"==typeof i){var c=n(i);c!==i&&(s(t)||(t[r]=c),i=c)}e[i]=!0}return e}function A(e){var t,n=u(null);for(t in e)!0===f(i,e,[t])&&(n[t]=e[t]);return n}function M(e,t){for(;null!==e;){var n=l(e,t);if(n){if(n.get)return T(n.get);if("function"==typeof n.value)return T(n.value)}e=c(e)}return function(e){return console.warn("fallback value for",e),null}}var L=m(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),R=m(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),P=m(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),O=m(["animate","color-profile","cursor","discard","fedropshadow","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),I=m(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"]),D=m(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),V=m(["#text"]),H=m(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),U=m(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),G=m(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),F=m(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),z=d(/\{\{[\w\W]*|[\w\W]*\}\}/gm),B=d(/<%[\w\W]*|[\w\W]*%>/gm),$=d(/\${[\w\W]*}/gm),W=d(/^data-[\-\w.\u00B7-\uFFFF]/),Z=d(/^aria-[\-\w]+$/),q=d(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),K=d(/^(?:\w+script|data):/i),Y=d(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),X=d(/^html$/i),J=d(/^[a-z][.\w]*(-[.\w]+)+$/i),Q=function(){return"undefined"==typeof window?null:window};return function t(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Q(),r=function(e){return t(e)};if(r.version="2.5.7",r.removed=[],!n||!n.document||9!==n.document.nodeType)return r.isSupported=!1,r;var i=n.document,a=n.document,s=n.DocumentFragment,c=n.HTMLTemplateElement,l=n.Node,d=n.Element,u=n.NodeFilter,p=n.NamedNodeMap,f=void 0===p?n.NamedNodeMap||n.MozNamedAttrMap:p,h=n.HTMLFormElement,g=n.DOMParser,T=n.trustedTypes,ee=d.prototype,te=M(ee,"cloneNode"),ne=M(ee,"nextSibling"),oe=M(ee,"childNodes"),re=M(ee,"parentNode");if("function"==typeof c){var ie=a.createElement("template");ie.content&&ie.content.ownerDocument&&(a=ie.content.ownerDocument)}var ae=function(t,n){if("object"!==e(t)||"function"!=typeof t.createPolicy)return null;var o=null,r="data-tt-policy-suffix";n.currentScript&&n.currentScript.hasAttribute(r)&&(o=n.currentScript.getAttribute(r));var i="dompurify"+(o?"#"+o:"");try{return t.createPolicy(i,{createHTML:function(e){return e},createScriptURL:function(e){return e}})}catch(e){return console.warn("TrustedTypes policy "+i+" could not be created."),null}}(T,i),se=ae?ae.createHTML(""):"",ce=a,le=ce.implementation,me=ce.createNodeIterator,de=ce.createDocumentFragment,ue=ce.getElementsByTagName,pe=i.importNode,fe={};try{fe=A(a).documentMode?a.documentMode:{}}catch(e){}var he={};r.isSupported="function"==typeof re&&le&&void 0!==le.createHTMLDocument&&9!==fe;var ge,we,ve=z,xe=B,Ce=$,Ee=W,ye=Z,_e=K,be=Y,Ne=J,Se=q,ke=null,Te=j({},[].concat(o(L),o(R),o(P),o(I),o(V))),je=null,Ae=j({},[].concat(o(H),o(U),o(G),o(F))),Me=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Le=null,Re=null,Pe=!0,Oe=!0,Ie=!1,De=!0,Ve=!1,He=!0,Ue=!1,Ge=!1,Fe=!1,ze=!1,Be=!1,$e=!1,We=!0,Ze=!1,qe=!0,Ke=!1,Ye={},Xe=null,Je=j({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),Qe=null,et=j({},["audio","video","img","source","image","track"]),tt=null,nt=j({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),ot="http://www.w3.org/1998/Math/MathML",rt="http://www.w3.org/2000/svg",it="http://www.w3.org/1999/xhtml",at=it,st=!1,ct=null,lt=j({},[ot,rt,it],E),mt=["application/xhtml+xml","text/html"],dt=null,ut=a.createElement("form"),pt=function(e){return e instanceof RegExp||e instanceof Function},ft=function(t){dt&&dt===t||(t&&"object"===e(t)||(t={}),t=A(t),ge=ge=-1===mt.indexOf(t.PARSER_MEDIA_TYPE)?"text/html":t.PARSER_MEDIA_TYPE,we="application/xhtml+xml"===ge?E:C,ke="ALLOWED_TAGS"in t?j({},t.ALLOWED_TAGS,we):Te,je="ALLOWED_ATTR"in t?j({},t.ALLOWED_ATTR,we):Ae,ct="ALLOWED_NAMESPACES"in t?j({},t.ALLOWED_NAMESPACES,E):lt,tt="ADD_URI_SAFE_ATTR"in t?j(A(nt),t.ADD_URI_SAFE_ATTR,we):nt,Qe="ADD_DATA_URI_TAGS"in t?j(A(et),t.ADD_DATA_URI_TAGS,we):et,Xe="FORBID_CONTENTS"in t?j({},t.FORBID_CONTENTS,we):Je,Le="FORBID_TAGS"in t?j({},t.FORBID_TAGS,we):{},Re="FORBID_ATTR"in t?j({},t.FORBID_ATTR,we):{},Ye="USE_PROFILES"in t&&t.USE_PROFILES,Pe=!1!==t.ALLOW_ARIA_ATTR,Oe=!1!==t.ALLOW_DATA_ATTR,Ie=t.ALLOW_UNKNOWN_PROTOCOLS||!1,De=!1!==t.ALLOW_SELF_CLOSE_IN_ATTR,Ve=t.SAFE_FOR_TEMPLATES||!1,He=!1!==t.SAFE_FOR_XML,Ue=t.WHOLE_DOCUMENT||!1,ze=t.RETURN_DOM||!1,Be=t.RETURN_DOM_FRAGMENT||!1,$e=t.RETURN_TRUSTED_TYPE||!1,Fe=t.FORCE_BODY||!1,We=!1!==t.SANITIZE_DOM,Ze=t.SANITIZE_NAMED_PROPS||!1,qe=!1!==t.KEEP_CONTENT,Ke=t.IN_PLACE||!1,Se=t.ALLOWED_URI_REGEXP||Se,at=t.NAMESPACE||it,Me=t.CUSTOM_ELEMENT_HANDLING||{},t.CUSTOM_ELEMENT_HANDLING&&pt(t.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(Me.tagNameCheck=t.CUSTOM_ELEMENT_HANDLING.tagNameCheck),t.CUSTOM_ELEMENT_HANDLING&&pt(t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(Me.attributeNameCheck=t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),t.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(Me.allowCustomizedBuiltInElements=t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Ve&&(Oe=!1),Be&&(ze=!0),Ye&&(ke=j({},o(V)),je=[],!0===Ye.html&&(j(ke,L),j(je,H)),!0===Ye.svg&&(j(ke,R),j(je,U),j(je,F)),!0===Ye.svgFilters&&(j(ke,P),j(je,U),j(je,F)),!0===Ye.mathMl&&(j(ke,I),j(je,G),j(je,F))),t.ADD_TAGS&&(ke===Te&&(ke=A(ke)),j(ke,t.ADD_TAGS,we)),t.ADD_ATTR&&(je===Ae&&(je=A(je)),j(je,t.ADD_ATTR,we)),t.ADD_URI_SAFE_ATTR&&j(tt,t.ADD_URI_SAFE_ATTR,we),t.FORBID_CONTENTS&&(Xe===Je&&(Xe=A(Xe)),j(Xe,t.FORBID_CONTENTS,we)),qe&&(ke["#text"]=!0),Ue&&j(ke,["html","head","body"]),ke.table&&(j(ke,["tbody"]),delete Le.tbody),m&&m(t),dt=t)},ht=j({},["mi","mo","mn","ms","mtext"]),gt=j({},["annotation-xml"]),wt=j({},["title","style","font","a","script"]),vt=j({},R);j(vt,P),j(vt,O);var xt=j({},I);j(xt,D);var Ct=function(e){x(r.removed,{element:e});try{e.parentNode.removeChild(e)}catch(t){try{e.outerHTML=se}catch(t){e.remove()}}},Et=function(e,t){try{x(r.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){x(r.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!je[e])if(ze||Be)try{Ct(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},yt=function(e){var t,n;if(Fe)e="<remove></remove>"+e;else{var o=y(e,/^[\r\n\t ]+/);n=o&&o[0]}"application/xhtml+xml"===ge&&at===it&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");var r=ae?ae.createHTML(e):e;if(at===it)try{t=(new g).parseFromString(r,ge)}catch(e){}if(!t||!t.documentElement){t=le.createDocument(at,"template",null);try{t.documentElement.innerHTML=st?se:r}catch(e){}}var i=t.body||t.documentElement;return e&&n&&i.insertBefore(a.createTextNode(n),i.childNodes[0]||null),at===it?ue.call(t,Ue?"html":"body")[0]:Ue?t.documentElement:i},_t=function(e){return me.call(e.ownerDocument||e,e,u.SHOW_ELEMENT|u.SHOW_COMMENT|u.SHOW_TEXT|u.SHOW_PROCESSING_INSTRUCTION|u.SHOW_CDATA_SECTION,null,!1)},bt=function(e){return e instanceof h&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof f)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},Nt=function(t){return"object"===e(l)?t instanceof l:t&&"object"===e(t)&&"number"==typeof t.nodeType&&"string"==typeof t.nodeName},St=function(e,t,n){he[e]&&w(he[e],(function(e){e.call(r,t,n,dt)}))},kt=function(e){var t;if(St("beforeSanitizeElements",e,null),bt(e))return Ct(e),!0;if(S(/[\u0080-\uFFFF]/,e.nodeName))return Ct(e),!0;var n=we(e.nodeName);if(St("uponSanitizeElement",e,{tagName:n,allowedTags:ke}),e.hasChildNodes()&&!Nt(e.firstElementChild)&&(!Nt(e.content)||!Nt(e.content.firstElementChild))&&S(/<[/\w]/g,e.innerHTML)&&S(/<[/\w]/g,e.textContent))return Ct(e),!0;if("select"===n&&S(/<template/i,e.innerHTML))return Ct(e),!0;if(7===e.nodeType)return Ct(e),!0;if(He&&8===e.nodeType&&S(/<[/\w]/g,e.data))return Ct(e),!0;if(!ke[n]||Le[n]){if(!Le[n]&&jt(n)){if(Me.tagNameCheck instanceof RegExp&&S(Me.tagNameCheck,n))return!1;if(Me.tagNameCheck instanceof Function&&Me.tagNameCheck(n))return!1}if(qe&&!Xe[n]){var o=re(e)||e.parentNode,i=oe(e)||e.childNodes;if(i&&o)for(var a=i.length-1;a>=0;--a){var s=te(i[a],!0);s.__removalCount=(e.__removalCount||0)+1,o.insertBefore(s,ne(e))}}return Ct(e),!0}return e instanceof d&&!function(e){var t=re(e);t&&t.tagName||(t={namespaceURI:at,tagName:"template"});var n=C(e.tagName),o=C(t.tagName);return!!ct[e.namespaceURI]&&(e.namespaceURI===rt?t.namespaceURI===it?"svg"===n:t.namespaceURI===ot?"svg"===n&&("annotation-xml"===o||ht[o]):Boolean(vt[n]):e.namespaceURI===ot?t.namespaceURI===it?"math"===n:t.namespaceURI===rt?"math"===n&&gt[o]:Boolean(xt[n]):e.namespaceURI===it?!(t.namespaceURI===rt&&!gt[o])&&!(t.namespaceURI===ot&&!ht[o])&&!xt[n]&&(wt[n]||!vt[n]):!("application/xhtml+xml"!==ge||!ct[e.namespaceURI]))}(e)?(Ct(e),!0):"noscript"!==n&&"noembed"!==n&&"noframes"!==n||!S(/<\/no(script|embed|frames)/i,e.innerHTML)?(Ve&&3===e.nodeType&&(t=e.textContent,t=_(t,ve," "),t=_(t,xe," "),t=_(t,Ce," "),e.textContent!==t&&(x(r.removed,{element:e.cloneNode()}),e.textContent=t)),St("afterSanitizeElements",e,null),!1):(Ct(e),!0)},Tt=function(e,t,n){if(We&&("id"===t||"name"===t)&&(n in a||n in ut))return!1;if(Oe&&!Re[t]&&S(Ee,t));else if(Pe&&S(ye,t));else if(!je[t]||Re[t]){if(!(jt(e)&&(Me.tagNameCheck instanceof RegExp&&S(Me.tagNameCheck,e)||Me.tagNameCheck instanceof Function&&Me.tagNameCheck(e))&&(Me.attributeNameCheck instanceof RegExp&&S(Me.attributeNameCheck,t)||Me.attributeNameCheck instanceof Function&&Me.attributeNameCheck(t))||"is"===t&&Me.allowCustomizedBuiltInElements&&(Me.tagNameCheck instanceof RegExp&&S(Me.tagNameCheck,n)||Me.tagNameCheck instanceof Function&&Me.tagNameCheck(n))))return!1}else if(tt[t]);else if(S(Se,_(n,be,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==b(n,"data:")||!Qe[e])if(Ie&&!S(_e,_(n,be,"")));else if(n)return!1;return!0},jt=function(e){return"annotation-xml"!==e&&y(e,Ne)},At=function(t){var n,o,i,a;St("beforeSanitizeAttributes",t,null);var s=t.attributes;if(s){var c={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:je};for(a=s.length;a--;){var l=n=s[a],m=l.name,d=l.namespaceURI;if(o="value"===m?n.value:N(n.value),i=we(m),c.attrName=i,c.attrValue=o,c.keepAttr=!0,c.forceKeepAttr=void 0,St("uponSanitizeAttribute",t,c),o=c.attrValue,!c.forceKeepAttr&&(Et(m,t),c.keepAttr))if(De||!S(/\/>/i,o)){Ve&&(o=_(o,ve," "),o=_(o,xe," "),o=_(o,Ce," "));var u=we(t.nodeName);if(Tt(u,i,o))if(!Ze||"id"!==i&&"name"!==i||(Et(m,t),o="user-content-"+o),He&&S(/((--!?|])>)|<\/(style|title)/i,o))Et(m,t);else{if(ae&&"object"===e(T)&&"function"==typeof T.getAttributeType)if(d);else switch(T.getAttributeType(u,i)){case"TrustedHTML":o=ae.createHTML(o);break;case"TrustedScriptURL":o=ae.createScriptURL(o)}try{d?t.setAttributeNS(d,m,o):t.setAttribute(m,o),bt(t)?Ct(t):v(r.removed)}catch(e){}}}else Et(m,t)}St("afterSanitizeAttributes",t,null)}},Mt=function e(t){var n,o=_t(t);for(St("beforeSanitizeShadowDOM",t,null);n=o.nextNode();)St("uponSanitizeShadowNode",n,null),kt(n)||(n.content instanceof s&&e(n.content),At(n));St("afterSanitizeShadowDOM",t,null)};return r.sanitize=function(t){var o,a,c,m,d,u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if((st=!t)&&(t="\x3c!--\x3e"),"string"!=typeof t&&!Nt(t)){if("function"!=typeof t.toString)throw k("toString is not a function");if("string"!=typeof(t=t.toString()))throw k("dirty is not a string, aborting")}if(!r.isSupported){if("object"===e(n.toStaticHTML)||"function"==typeof n.toStaticHTML){if("string"==typeof t)return n.toStaticHTML(t);if(Nt(t))return n.toStaticHTML(t.outerHTML)}return t}if(Ge||ft(u),r.removed=[],"string"==typeof t&&(Ke=!1),Ke){if(t.nodeName){var p=we(t.nodeName);if(!ke[p]||Le[p])throw k("root node is forbidden and cannot be sanitized in-place")}}else if(t instanceof l)1===(a=(o=yt("\x3c!----\x3e")).ownerDocument.importNode(t,!0)).nodeType&&"BODY"===a.nodeName||"HTML"===a.nodeName?o=a:o.appendChild(a);else{if(!ze&&!Ve&&!Ue&&-1===t.indexOf("<"))return ae&&$e?ae.createHTML(t):t;if(!(o=yt(t)))return ze?null:$e?se:""}o&&Fe&&Ct(o.firstChild);for(var f=_t(Ke?t:o);c=f.nextNode();)3===c.nodeType&&c===m||kt(c)||(c.content instanceof s&&Mt(c.content),At(c),m=c);if(m=null,Ke)return t;if(ze){if(Be)for(d=de.call(o.ownerDocument);o.firstChild;)d.appendChild(o.firstChild);else d=o;return(je.shadowroot||je.shadowrootmod)&&(d=pe.call(i,d,!0)),d}var h=Ue?o.outerHTML:o.innerHTML;return Ue&&ke["!doctype"]&&o.ownerDocument&&o.ownerDocument.doctype&&o.ownerDocument.doctype.name&&S(X,o.ownerDocument.doctype.name)&&(h="<!DOCTYPE "+o.ownerDocument.doctype.name+">\n"+h),Ve&&(h=_(h,ve," "),h=_(h,xe," "),h=_(h,Ce," ")),ae&&$e?ae.createHTML(h):h},r.setConfig=function(e){ft(e),Ge=!0},r.clearConfig=function(){dt=null,Ge=!1},r.isValidAttribute=function(e,t,n){dt||ft({});var o=we(e),r=we(t);return Tt(o,r,n)},r.addHook=function(e,t){"function"==typeof t&&(he[e]=he[e]||[],x(he[e],t))},r.removeHook=function(e){if(he[e])return v(he[e])},r.removeHooks=function(e){he[e]&&(he[e]=[])},r.removeAllHooks=function(){he={}},r}()}()},94931:(e,t,n)=>{"use strict";var o=n(51609),r=Symbol.for("react.element"),i=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,s=o.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function l(e,t,n){var o,i={},l=null,m=null;for(o in void 0!==n&&(l=""+n),void 0!==t.key&&(l=""+t.key),void 0!==t.ref&&(m=t.ref),t)a.call(t,o)&&!c.hasOwnProperty(o)&&(i[o]=t[o]);if(e&&e.defaultProps)for(o in t=e.defaultProps)void 0===i[o]&&(i[o]=t[o]);return{$$typeof:r,type:e,key:l,ref:m,props:i,_owner:s.current}}t.Fragment=i,t.jsx=l,t.jsxs=l},39793:(e,t,n)=>{"use strict";e.exports=n(94931)},51609:e=>{"use strict";e.exports=window.React}},t={};function n(o){var r=t[o];if(void 0!==r)return r.exports;var i=t[o]={exports:{}};return e[o].call(i.exports,i,i.exports,n),i.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{"use strict";var e={};n.r(e),n.d(e,{amazonEbayIntegration:()=>v,automatewoo:()=>x,automatewooAlt:()=>C,automatewooBirthdays:()=>x,automatewooReferrals:()=>x,blank:()=>w,creativeMailByConstantContact:()=>I,facebook:()=>j,facebookForWoocommerce:()=>j,googleAds:()=>M,googleListingsAndAds:()=>M,hubspotForWoocommerce:()=>L,integrationWithSalesforce:()=>V,klaviyo:()=>O,mailchimpForWoocommerce:()=>R,mailpoet:()=>P,pinterest:()=>A,pinterestForWoocommerce:()=>A,tiktokForBusiness:()=>H,trustpilotReviews:()=>F,vimeo:()=>G,woocommerceAmazonEbayIntegration:()=>v,woocommerceFreeGiftCoupons:()=>B,woocommerceGroupCoupons:()=>W,woocommerceSmartCoupons:()=>Z,woocommerceStoreCredit:()=>z,woocommerceUrlCoupons:()=>$,woocommerceZapier:()=>U,zeroBsCrm:()=>D});var t={};n.r(t),n.d(t,{activateInstalledPlugin:()=>ve,handleFetchError:()=>he,installAndActivateRecommendedPlugin:()=>xe,loadInstalledPluginsAfterActivation:()=>we,receiveActivatingPlugin:()=>me,receiveBlogPosts:()=>fe,receiveInstalledPlugins:()=>le,receiveMiscRecommendations:()=>pe,receiveRecommendedPlugins:()=>ue,removeActivatingPlugin:()=>de,setError:()=>ge});var o={};n.r(o),n.d(o,{getActivatingPlugins:()=>Ee,getBlogPosts:()=>be,getBlogPostsError:()=>Ne,getInstalledPlugins:()=>Ce,getMiscRecommendations:()=>_e,getRecommendedPlugins:()=>ye});var r={};n.r(r),n.d(r,{getBlogPosts:()=>Te,getMiscRecommendations:()=>ke,getRecommendedPlugins:()=>Se});const i=window.wp.element,a=window.wp.i18n,s=window.wc.data,c=window.wp.compose;function l(e){var t,n,o="";if("string"==typeof e||"number"==typeof e)o+=e;else if("object"==typeof e)if(Array.isArray(e)){var r=e.length;for(t=0;t<r;t++)e[t]&&(n=l(e[t]))&&(o&&(o+=" "),o+=n)}else for(n in e)e[n]&&(o&&(o+=" "),o+=n);return o}const m=function(){for(var e,t,n=0,o="",r=arguments.length;n<r;n++)(e=arguments[n])&&(t=l(e))&&(o&&(o+=" "),o+=t);return o},d=window.wp.data,u=window.wc.tracks,p=(0,i.forwardRef)((function({icon:e,size:t=24,...n},o){return(0,i.cloneElement)(e,{width:t,height:t,...n,ref:o})})),f=window.lodash,h=window.wp.primitives;var g=n(39793);const w=(0,g.jsx)(h.SVG,{width:"36",height:"36",fill:"none",xmlns:"http://www.w3.org/2000/svg"}),v=(0,g.jsxs)(h.SVG,{xmlns:"http://www.w3.org/2000/svg",width:"100",height:"100",viewBox:"0 0 100 100",children:[(0,g.jsx)("defs",{children:(0,g.jsx)("clipPath",{id:"b",children:(0,g.jsx)("rect",{width:"100",height:"100"})})}),(0,g.jsxs)("g",{id:"a",clipPath:"url(#b)",children:[(0,g.jsx)("rect",{width:"100",height:"100",fill:"#fff"}),(0,g.jsx)("rect",{width:"100",height:"100",fill:"#eee"}),(0,g.jsxs)("g",{transform:"translate(9 25.655)",children:[(0,g.jsx)(h.Path,{d:"M179.753,195.8c-4.732,3.488-11.591,5.349-17.5,5.349a31.66,31.66,0,0,1-21.374-8.156c-.443-.4-.046-.946.486-.634a43.018,43.018,0,0,0,21.384,5.671,42.523,42.523,0,0,0,16.312-3.335c.8-.34,1.471.525.688,1.106",transform:"translate(-129.235 -176.611)",fill:"#f90",fillRule:"evenodd"}),(0,g.jsx)(h.Path,{d:"M577.807,183.949c-.6-.773-4-.365-5.522-.184-.464.057-.535-.347-.117-.638,2.7-1.9,7.142-1.354,7.66-.716s-.135,5.09-2.676,7.213c-.39.326-.762.152-.588-.28.571-1.425,1.85-4.619,1.244-5.395",transform:"translate(-525.323 -167.01)",fill:"#f90",fillRule:"evenodd"}),(0,g.jsx)(h.Path,{d:"M572.708,6.758V4.908a.457.457,0,0,1,.468-.468h8.284a.461.461,0,0,1,.479.468V6.493a2.605,2.605,0,0,1-.624,1.163l-4.292,6.129a9.146,9.146,0,0,1,4.725,1.014.843.843,0,0,1,.44.72v1.974c0,.269-.3.585-.61.422a9.542,9.542,0,0,0-8.752.014c-.287.156-.588-.156-.588-.425V15.627a2.238,2.238,0,0,1,.3-1.272l4.973-7.132h-4.328a.458.458,0,0,1-.479-.464",transform:"translate(-525.64 -4.078)",fillRule:"evenodd"}),(0,g.jsx)(h.Path,{d:"M173.431,15.624h-2.52a.476.476,0,0,1-.45-.429V2.261a.473.473,0,0,1,.486-.464h2.35a.475.475,0,0,1,.457.432V3.92h.046a3.463,3.463,0,0,1,6.589,0,3.722,3.722,0,0,1,6.4-.982c.8,1.088.634,2.669.634,4.055l0,8.163a.476.476,0,0,1-.486.468h-2.517a.479.479,0,0,1-.454-.468V8.3a16.192,16.192,0,0,0-.071-2.424,1.312,1.312,0,0,0-1.482-1.113,1.674,1.674,0,0,0-1.506,1.06,7.831,7.831,0,0,0-.234,2.478v6.855a.476.476,0,0,1-.486.468h-2.517a.476.476,0,0,1-.454-.468l0-6.855c0-1.443.238-3.566-1.553-3.566-1.811,0-1.74,2.07-1.74,3.566v6.855a.476.476,0,0,1-.486.468",transform:"translate(-156.58 -1.399)",fillRule:"evenodd"}),(0,g.jsx)(h.Path,{d:"M714.982,1.524c3.739,0,5.763,3.211,5.763,7.295,0,3.945-2.237,7.075-5.763,7.075-3.672,0-5.671-3.211-5.671-7.213,0-4.027,2.024-7.156,5.671-7.156M715,4.164c-1.857,0-1.974,2.531-1.974,4.108s-.025,4.955,1.953,4.955c1.953,0,2.045-2.722,2.045-4.381a11.959,11.959,0,0,0-.376-3.431A1.577,1.577,0,0,0,715,4.164",transform:"translate(-651.552 -1.399)",fillRule:"evenodd"}),(0,g.jsx)(h.Path,{d:"M875.817,15.624h-2.51a.479.479,0,0,1-.454-.468l0-12.938a.477.477,0,0,1,.486-.422h2.336a.482.482,0,0,1,.45.362V4.136h.046c.705-1.769,1.694-2.612,3.435-2.612a3.307,3.307,0,0,1,2.942,1.524c.659,1.035.659,2.775.659,4.027v8.142a.484.484,0,0,1-.486.408h-2.527a.477.477,0,0,1-.447-.408V8.191c0-1.414.163-3.484-1.577-3.484a1.647,1.647,0,0,0-1.457,1.035,5.724,5.724,0,0,0-.4,2.449v6.965a.485.485,0,0,1-.493.468",transform:"translate(-801.775 -1.399)",fillRule:"evenodd"}),(0,g.jsx)(h.Path,{d:"M413.163,8.046a4.93,4.93,0,0,1-.471,2.673,2.048,2.048,0,0,1-1.744,1.145c-.968,0-1.535-.737-1.535-1.825,0-2.148,1.925-2.538,3.75-2.538v.546m2.541,6.143a.526.526,0,0,1-.6.06,6.143,6.143,0,0,1-1.446-1.68,4.991,4.991,0,0,1-4.154,1.833,3.575,3.575,0,0,1-3.771-3.927,4.277,4.277,0,0,1,2.687-4.119,17.463,17.463,0,0,1,4.739-.876V5.154a3.214,3.214,0,0,0-.308-1.825,1.677,1.677,0,0,0-1.414-.656,1.917,1.917,0,0,0-2.024,1.514.527.527,0,0,1-.439.461l-2.442-.262a.444.444,0,0,1-.376-.528C406.719.893,409.4,0,411.795,0a5.714,5.714,0,0,1,3.8,1.255C416.818,2.4,416.7,3.928,416.7,5.59V9.517a3.447,3.447,0,0,0,.95,2.336.477.477,0,0,1-.011.67c-.514.429-1.428,1.226-1.932,1.673l0-.007",transform:"translate(-372.698 0)",fillRule:"evenodd"}),(0,g.jsx)(h.Path,{d:"M7.426,8.046a4.93,4.93,0,0,1-.471,2.673,2.043,2.043,0,0,1-1.744,1.145c-.968,0-1.531-.737-1.531-1.825C3.679,7.89,5.6,7.5,7.426,7.5v.546m2.541,6.143a.526.526,0,0,1-.6.06,6.2,6.2,0,0,1-1.446-1.68A4.986,4.986,0,0,1,3.771,14.4,3.576,3.576,0,0,1,0,10.474,4.282,4.282,0,0,1,2.687,6.356,17.462,17.462,0,0,1,7.426,5.48V5.154a3.243,3.243,0,0,0-.3-1.825,1.686,1.686,0,0,0-1.414-.656A1.921,1.921,0,0,0,3.679,4.186a.527.527,0,0,1-.436.461L.8,4.385a.446.446,0,0,1-.376-.528C.985.893,3.662,0,6.058,0a5.714,5.714,0,0,1,3.8,1.255C11.08,2.4,10.963,3.928,10.963,5.59V9.517a3.447,3.447,0,0,0,.95,2.336.473.473,0,0,1-.007.67c-.514.429-1.428,1.226-1.932,1.673l-.007-.007",transform:"translate(0 0)",fillRule:"evenodd"})]}),(0,g.jsxs)("g",{transform:"translate(18.9 54.637)",children:[(0,g.jsx)(h.Path,{d:"M8.055,26.308C3.716,26.308.1,28.149.1,33.7c0,4.4,2.431,7.171,8.067,7.171,6.633,0,7.059-4.37,7.059-4.37H12.011s-.689,2.353-4.04,2.353a4.4,4.4,0,0,1-4.693-4.428H15.562V32.807c0-2.557-1.623-6.5-7.507-6.5Zm-.112,2.073c2.6,0,4.37,1.592,4.37,3.977H3.349C3.349,29.826,5.661,28.381,7.943,28.381Z",transform:"translate(0 -20.83)",fill:"#e53238"}),(0,g.jsx)(h.Path,{d:"M75.169.1V17.254c0,.974-.069,2.341-.069,2.341h3.066s.11-.982.11-1.879c0,0,1.515,2.37,5.633,2.37a6.961,6.961,0,0,0,7.283-7.325A6.922,6.922,0,0,0,83.915,5.52c-4.279,0-5.609,2.311-5.609,2.311V.1Zm7.955,7.542c2.945,0,4.818,2.186,4.818,5.119a4.857,4.857,0,0,1-4.8,5.2c-3.143,0-4.839-2.454-4.839-5.175C78.306,10.254,79.827,7.642,83.123,7.642Z",transform:"translate(-59.609)",fill:"#0064d2"}),(0,g.jsx)(h.Path,{d:"M159.834,26.308c-6.528,0-6.947,3.574-6.947,4.146h3.249s.17-2.087,3.473-2.087c2.146,0,3.809.982,3.809,2.871v.672h-3.809c-5.057,0-7.731,1.479-7.731,4.482,0,2.955,2.47,4.562,5.809,4.562,4.55,0,6.015-2.514,6.015-2.514,0,1,.077,1.985.077,1.985h2.888s-.112-1.221-.112-2V31.669c0-4.428-3.572-5.36-6.722-5.36Zm3.585,7.619v.9c0,1.169-.721,4.075-4.968,4.075-2.326,0-3.323-1.161-3.323-2.507C155.128,33.943,158.486,33.927,163.419,33.927Z",transform:"translate(-120.634 -20.83)",fill:"#f5af02"}),(0,g.jsx)(h.Path,{d:"M214.879,29.041h3.655l5.246,10.51,5.234-10.51h3.311l-9.533,18.711h-3.473l2.751-5.216Z",transform:"translate(-170.706 -23.002)",fill:"#86b817"})]})]})]}),x=(0,g.jsxs)(h.SVG,{xmlns:"http://www.w3.org/2000/svg",width:"100",height:"100",viewBox:"0 0 100 100",children:[(0,g.jsx)("defs",{children:(0,g.jsx)("clipPath",{id:"b",children:(0,g.jsx)("rect",{width:"100",height:"100"})})}),(0,g.jsxs)("g",{id:"a",clipPath:"url(#b)",children:[(0,g.jsx)("rect",{width:"100",height:"100",fill:"#fff"}),(0,g.jsx)("rect",{width:"100",height:"100",fill:"#7532e4"}),(0,g.jsxs)("g",{transform:"translate(-43.503 -133.512)",children:[(0,g.jsx)(h.Path,{d:"M78.217,193.13H64.405l-2.823,7.764H54.6L67.648,166.9h7.669l12.934,33.995H81.059Zm-11.6-6.047h9.4L71.33,174.245Z",transform:"translate(0 0)",fill:"#1ff2e6"}),(0,g.jsx)(h.Path,{d:"M246.639,166.9h6.753l-9.4,33.995h-6.81l-7.764-24.208-7.764,24.208h-6.906L205.3,166.9h7l6.238,23.388,7.535-23.388h6.849l7.592,23.483Z",transform:"translate(-121.952)",fill:"#1ff2e6"})]})]})]}),C=(0,g.jsx)(h.SVG,{xmlns:"http://www.w3.org/2000/svg",children:(0,g.jsx)(h.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M4.67708 14.1615h3.77084l.77604 2.1198h1.96354L7.65625 7H5.5625L2 16.2813h1.90625l.77083-2.1198zm3.17188-1.6511H5.28125l1.28646-3.50519 1.28125 3.50519zM22.9791 7h-1.8437l-1.6719 6.4115L17.3906 7h-1.8698l-2.0573 6.3854L11.7604 7H9.8489l2.5781 9.2813h1.8854l2.1198-6.60942 2.1198 6.60942h1.8594L22.9791 7z"})}),E=window.wc.wcSettings,y=["wcAdminSettings","preloadSettings"],_=(0,E.getSetting)("admin",{}),b=Object.keys(_).reduce(((e,t)=>(y.includes(t)||(e[t]=_[t]),e)),{}),N={onboarding:{profile:"Deprecated: wcSettings.admin.onboarding.profile is deprecated. It is planned to be released in WooCommerce 10.0.0. Please use `getProfileItems` from the onboarding store. See https://github.com/woocommerce/woocommerce/tree/trunk/packages/js/data/src/onboarding for more information.",euCountries:"Deprecated: wcSettings.admin.onboarding.euCountries is deprecated. Please use `/wc/v3/data/continents/eu` from the REST API. See https://woocommerce.github.io/woocommerce-rest-api-docs/#list-all-continents for more information.",localInfo:'Deprecated: wcSettings.admin.onboarding.localInfo is deprecated. Please use `include WC()->plugin_path() . "/i18n/locale-info.php"` instead.',currencySymbols:'"Deprecated: wcSettings.admin.onboarding.currencySymbols is deprecated. Please use get_woocommerce_currency_symbols() function instead.'}};function S(e,t=!1,n=e=>e,o=N){if(y.includes(e))throw new Error((0,a.__)("Mutable settings should be accessed via data store.","woocommerce"));return n(b.hasOwnProperty(e)?b[e]:t,t)}(0,E.getSetting)("adminUrl"),(0,E.getSetting)("countries"),(0,E.getSetting)("currency");const k=(0,E.getSetting)("locale"),T=((0,E.getSetting)("siteTitle"),(0,E.getSetting)("wcAssetUrl"));S("orderStatuses");const j=(0,g.jsx)("img",{src:`${T}images/marketing/facebook.svg`,alt:(0,a.__)("Facebook","woocommerce"),style:{padding:"4px"}}),A=(0,g.jsx)(h.SVG,{width:"303",height:"303",viewBox:"-30 -30 303 303",fill:"none",xmlns:"http://www.w3.org/2000/SVG",children:(0,g.jsx)(h.Path,{fill:"#E60023",d:"M121.5,0C54.4,0,0,54.4,0,121.5C0,173,32,217,77.2,234.7c-1.1-9.6-2-24.4,0.4-34.9 c2.2-9.5,14.2-60.4,14.2-60.4s-3.6-7.3-3.6-18c0-16.9,9.8-29.5,22-29.5c10.4,0,15.4,7.8,15.4,17.1c0,10.4-6.6,26-10.1,40.5 c-2.9,12.1,6.1,22,18,22c21.6,0,38.2-22.8,38.2-55.6c0-29.1-20.9-49.4-50.8-49.4C86.3,66.5,66,92.4,66,119.2c0,10.4,4,21.6,9,27.7 c1,1.2,1.1,2.3,0.8,3.5c-0.9,3.8-3,12.1-3.4,13.8c-0.5,2.2-1.8,2.7-4.1,1.6c-15.2-7.1-24.7-29.2-24.7-47.1 c0-38.3,27.8-73.5,80.3-73.5c42.1,0,74.9,30,74.9,70.2c0,41.9-26.4,75.6-63,75.6c-12.3,0-23.9-6.4-27.8-14c0,0-6.1,23.2-7.6,28.9 c-2.7,10.6-10.1,23.8-15.1,31.9c11.4,3.5,23.4,5.4,36,5.4c67.1,0,121.5-54.4,121.5-121.5C243,54.4,188.6,0,121.5,0z"})}),M=(0,g.jsx)("img",{src:`${T}images/marketing/google.svg`,alt:(0,a.__)("Google","woocommerce"),style:{padding:"4px"}}),L=(0,g.jsxs)(h.SVG,{xmlns:"http://www.w3.org/2000/svg",width:"100",height:"100",viewBox:"0 0 100 100",children:[(0,g.jsx)("defs",{children:(0,g.jsx)("clipPath",{id:"b",children:(0,g.jsx)("rect",{width:"100",height:"100"})})}),(0,g.jsxs)("g",{id:"a",clipPath:"url(#b)",children:[(0,g.jsx)("rect",{width:"100",height:"100",fill:"#fff"}),(0,g.jsx)(h.Path,{d:"M100,100H0V0H100V100ZM40.665,75.539a6.446,6.446,0,1,0,6.447,6.447,6.376,6.376,0,0,0-.3-1.843L54.158,72.8A19.808,19.808,0,1,0,69.206,37.48h.015V28.455a6.959,6.959,0,0,0,4.013-6.273v-.211a6.971,6.971,0,0,0-6.952-6.953H66.07a6.97,6.97,0,0,0-6.952,6.953v.211a6.957,6.957,0,0,0,4.013,6.273V37.5a19.745,19.745,0,0,0-9.376,4.126L28.935,22.295a7.919,7.919,0,0,0-4.148-9.145,7.845,7.845,0,0,0-3.5-.817,7.919,7.919,0,1,0,3.938,14.786l24.4,19a19.775,19.775,0,0,0,.3,22.3l-7.426,7.427A6.362,6.362,0,0,0,40.665,75.539Zm25.522-8.321h0l-.023,0a10.164,10.164,0,0,1,.023-20.328H66.2a10.166,10.166,0,0,1-.012,20.333Z",fill:"#ff7a59"})]})]}),R=(0,g.jsxs)(h.SVG,{width:"36",height:"36",viewBox:"0 0 36 36",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,g.jsx)("rect",{width:"36",height:"36",rx:"3",fill:"#FFE071"}),(0,g.jsx)(h.Path,{d:"M24.0534 17.2863C24.2392 17.2638 24.4176 17.2625 24.5813 17.2863C24.6764 17.0647 24.6923 16.6823 24.6071 16.2661C24.4808 15.6471 24.3091 15.2728 23.9546 15.331C23.6002 15.3892 23.5873 15.8374 23.7143 16.4564C23.7848 16.8043 23.9117 17.1023 24.0534 17.2863Z",fill:"black"}),(0,g.jsx)(h.Path,{d:"M21.0119 17.7757C21.2652 17.8889 21.4209 17.9647 21.4823 17.899C21.5215 17.8576 21.5099 17.7794 21.4491 17.6786C21.3241 17.4702 21.0665 17.2587 20.7937 17.1404C20.2357 16.895 19.5697 16.9764 19.0559 17.3532C18.886 17.4802 18.7254 17.6555 18.7487 17.7625C18.756 17.7969 18.7812 17.8232 18.8413 17.8314C18.9811 17.8476 19.4698 17.5954 20.0321 17.5603C20.4294 17.5353 20.7587 17.6624 21.0119 17.7757Z",fill:"black"}),(0,g.jsx)(h.Path,{d:"M20.5024 18.073C20.1725 18.1262 19.9904 18.237 19.8733 18.3409C19.7733 18.4298 19.712 18.5281 19.7126 18.5975C19.7126 18.6307 19.7267 18.6495 19.7378 18.6589C19.7531 18.6726 19.7709 18.6802 19.7923 18.6802C19.8671 18.6802 20.0339 18.6119 20.0339 18.6119C20.4932 18.4442 20.7961 18.4642 21.0966 18.4993C21.2627 18.518 21.3406 18.5287 21.3774 18.4705C21.3884 18.4536 21.4013 18.4179 21.3682 18.3628C21.2903 18.2339 20.9568 18.0179 20.5024 18.073Z",fill:"black"}),(0,g.jsx)(h.Path,{d:"M23.0263 19.1626C23.2501 19.2753 23.4972 19.2309 23.5775 19.0644C23.6578 18.8973 23.5413 18.6713 23.3169 18.5587C23.0925 18.446 22.846 18.4904 22.7657 18.6569C22.6859 18.824 22.8025 19.0506 23.0263 19.1626Z",fill:"black"}),(0,g.jsx)(h.Path,{d:"M24.4673 17.8777C24.2851 17.8746 24.1343 18.0786 24.13 18.3334C24.1257 18.5881 24.2698 18.7971 24.4519 18.8003C24.634 18.8034 24.7849 18.5994 24.7892 18.3446C24.7935 18.0899 24.6494 17.8809 24.4673 17.8777Z",fill:"black"}),(0,g.jsx)(h.Path,{d:"M12.2373 22.4735C12.1919 22.4153 12.1177 22.4335 12.0454 22.4504C11.9951 22.4623 11.9381 22.476 11.8755 22.4748C11.7419 22.4723 11.6284 22.4134 11.5646 22.3139C11.4819 22.1837 11.4868 21.9903 11.5781 21.7682C11.5904 21.7381 11.6051 21.7049 11.6211 21.6686C11.767 21.3344 12.0117 20.7743 11.7369 20.241C11.5303 19.8398 11.1937 19.5895 10.7884 19.5369C10.3996 19.4868 9.99919 19.6339 9.7441 19.9212C9.34124 20.3749 9.27808 20.9921 9.35595 21.2099C9.38477 21.29 9.42892 21.3119 9.46142 21.3163C9.5301 21.3257 9.63127 21.275 9.69505 21.1003C9.69934 21.0878 9.70547 21.0684 9.71344 21.0434C9.74165 20.9514 9.79438 20.7799 9.88084 20.6422C9.98508 20.4763 10.147 20.3618 10.3371 20.3205C10.5308 20.2779 10.7289 20.3161 10.8944 20.4269C11.1765 20.6153 11.285 20.9683 11.1648 21.305C11.1023 21.479 11.0011 21.812 11.0238 22.0855C11.0692 22.6394 11.4028 22.8616 11.7026 22.8854C11.9939 22.8966 12.1981 22.7295 12.2496 22.6075C12.279 22.5361 12.2539 22.4923 12.2373 22.4735Z",fill:"black"}),(0,g.jsx)(h.Path,{d:"M29.0624 21.4609C29.0513 21.4209 28.979 21.1511 28.8796 20.8263C28.7803 20.5015 28.6773 20.2724 28.6773 20.2724C29.0759 19.6634 29.0826 19.1189 29.0299 18.8109C28.9735 18.4285 28.8177 18.1031 28.5031 17.7663C28.1892 17.4296 27.5466 17.0847 26.6434 16.8262C26.5403 16.7968 26.1994 16.7011 26.1694 16.6917C26.1669 16.6717 26.1442 15.5513 26.124 15.0706C26.1093 14.7233 26.0798 14.18 25.9149 13.6455C25.7181 12.922 25.3759 12.2886 24.9479 11.8836C26.1283 10.635 26.8647 9.25926 26.8629 8.07947C26.8592 5.81 24.1293 5.1234 20.7642 6.54542C20.7605 6.54667 20.0565 6.85147 20.051 6.8546C20.048 6.85147 18.7621 5.56402 18.7431 5.5465C14.907 2.13103 2.91255 15.7391 6.7474 19.0444L7.58562 19.7692C7.36794 20.3437 7.28271 21.0028 7.35261 21.7107C7.44213 22.6201 7.90202 23.4926 8.64704 24.166C9.35404 24.8057 10.2842 25.2106 11.1868 25.21C12.6793 28.72 16.0886 30.8737 20.0872 30.9951C24.3758 31.1253 27.9758 29.0711 29.4842 25.3815C29.583 25.1224 30.0018 23.9557 30.0018 22.9255C30.0005 21.8903 29.4272 21.4609 29.0624 21.4609ZM11.5161 24.2236C11.3861 24.2461 11.2531 24.2555 11.1188 24.2518C9.82374 24.2161 8.42445 23.0263 8.28526 21.6143C8.13135 20.054 8.91255 18.8535 10.2953 18.5687C10.4608 18.5349 10.6601 18.5149 10.876 18.5268C11.651 18.57 12.7928 19.1777 13.0534 20.9002C13.2845 22.4261 12.9172 23.9801 11.5161 24.2236ZM10.0696 17.6361C9.20872 17.807 8.45021 18.3052 7.98603 18.9931C7.70887 18.7571 7.19195 18.3002 7.10059 18.1218C6.35986 16.686 7.90877 13.8946 8.99104 12.318C11.6657 8.42245 15.8544 5.4739 17.7939 6.00903C18.1091 6.10041 19.1533 7.33591 19.1533 7.33591C19.1533 7.33591 17.2151 8.43372 15.4172 9.96402C12.9951 11.8667 11.1654 14.6338 10.0696 17.6361ZM23.6657 23.6403C23.694 23.6284 23.7136 23.5952 23.7099 23.5627C23.7056 23.5226 23.6706 23.4932 23.6314 23.4976C23.6314 23.4976 21.6024 23.8043 19.6856 23.0876C19.8941 22.3948 20.4496 22.6451 21.2884 22.714C22.8012 22.806 24.1563 22.5807 25.1582 22.2871C26.0265 22.033 27.1664 21.5317 28.0525 20.8182C28.3511 21.4879 28.4565 22.2252 28.4565 22.2252C28.4565 22.2252 28.6877 22.1832 28.8809 22.304C29.0636 22.4186 29.1973 22.657 29.1059 23.2735C28.9195 24.4252 28.44 25.3596 27.6343 26.2196C27.1437 26.7585 26.5477 27.2273 25.8665 27.5684C25.5047 27.7624 25.119 27.9301 24.7118 28.0659C21.6735 29.0786 18.5628 27.9652 17.5603 25.5737C17.4799 25.394 17.4125 25.2056 17.3592 25.0091C16.9318 23.4331 17.2948 21.5423 18.4285 20.3525V20.3519C18.4984 20.2761 18.5696 20.1866 18.5696 20.0746C18.5696 19.9807 18.5113 19.8818 18.4604 19.8111C18.0637 19.224 16.6896 18.2232 16.9655 16.2861C17.1635 14.8948 18.3556 13.9146 19.4673 13.9728C19.5611 13.9778 19.6549 13.9835 19.7487 13.9891C20.2307 14.0179 20.6507 14.0811 21.0468 14.098C21.7103 14.1274 22.3069 14.0285 23.0139 13.4277C23.2525 13.2249 23.4438 13.049 23.7669 12.9933C23.8006 12.9877 23.8853 12.9564 24.0545 12.9645C24.2268 12.9739 24.3911 13.0221 24.5389 13.1222C25.1055 13.5072 25.1858 14.4391 25.2153 15.1213C25.2318 15.5106 25.2778 16.4526 25.2937 16.7224C25.3299 17.3407 25.4887 17.4277 25.8113 17.536C25.9922 17.5967 26.1608 17.6424 26.4085 17.7131C27.1584 17.9278 27.603 18.1462 27.8838 18.426C28.0512 18.6013 28.1285 18.7872 28.153 18.9643C28.2413 19.6227 27.6521 20.4364 26.0921 21.1755C24.3868 21.9836 22.3174 22.1882 20.888 22.0255C20.7783 22.013 20.3883 21.9679 20.3871 21.9679C19.2435 21.8108 18.591 23.3192 19.2778 24.3525C19.7199 25.0185 20.9248 25.4522 22.1303 25.4522C24.8939 25.4529 27.0186 24.248 27.8084 23.2078C27.8323 23.1765 27.8342 23.1734 27.8716 23.1158C27.9102 23.0557 27.8783 23.0232 27.8299 23.057C27.1842 23.5076 24.3169 25.2976 21.2492 24.7594C21.2492 24.7594 20.8764 24.6968 20.5361 24.5616C20.2656 24.4546 19.6997 24.1886 19.631 23.5958C22.107 24.3788 23.6657 23.6403 23.6657 23.6403ZM19.7444 23.1677C19.7444 23.1684 19.7444 23.1684 19.7444 23.1677C19.745 23.169 19.745 23.169 19.745 23.1696C19.745 23.169 19.7444 23.1684 19.7444 23.1677ZM15.0088 12.3023C15.9599 11.1807 17.1304 10.2056 18.1784 9.65858C18.2145 9.6398 18.2532 9.67986 18.2336 9.71616C18.1502 9.87013 17.9901 10.1993 17.9392 10.4497C17.9313 10.4885 17.9729 10.5179 18.0048 10.4954C18.6573 10.0416 19.7916 9.55531 20.7875 9.49272C20.8304 9.49022 20.8506 9.54592 20.8169 9.57283C20.6654 9.69113 20.4999 9.85511 20.3791 10.021C20.3582 10.0491 20.3779 10.0898 20.4122 10.0898C21.1112 10.0948 22.0966 10.3446 22.7386 10.712C22.7821 10.737 22.7509 10.8227 22.7024 10.8115C21.7305 10.5843 20.1406 10.4115 18.488 10.8227C17.0133 11.1901 15.8875 11.7572 15.0665 12.3668C15.0254 12.3981 14.9757 12.3418 15.0088 12.3023Z",fill:"black"})]}),P=(0,g.jsxs)(h.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"-30 -30 212.02 216.4",children:[(0,g.jsx)(h.Path,{fill:"#fe5301",d:"M37.71,89.1c3.5,0,5.9-.8,7.2-2.3a8,8,0,0,0,2-5.4V35.7l17,45.1a12.68,12.68,0,0,0,3.7,5.4c1.6,1.3,4,2,7.2,2a12.54,12.54,0,0,0,5.9-1.4,8.41,8.41,0,0,0,3.9-5l18.1-50V81a8.53,8.53,0,0,0,2.1,6.1c1.4,1.4,3.7,2.2,6.9,2.2,3.5,0,5.9-.8,7.2-2.3a8,8,0,0,0,2-5.4V8.7a7.48,7.48,0,0,0-3.3-6.6c-2.1-1.4-5-2.1-8.6-2.1a19.3,19.3,0,0,0-9.4,2,11.63,11.63,0,0,0-5.1,6.8L74.91,67.1,54.41,8.4a12.4,12.4,0,0,0-4.5-6.2c-2.1-1.5-5-2.2-8.8-2.2a16.51,16.51,0,0,0-8.9,2.1c-2.3,1.5-3.5,3.9-3.5,7.2V80.8c0,2.8.7,4.8,2,6.2C32.21,88.4,34.41,89.1,37.71,89.1Z"}),(0,g.jsx)(h.Path,{fill:"#fe5301",d:"M149,116.6l-2.4-1.9a7.4,7.4,0,0,0-9.4.3,19.65,19.65,0,0,1-12.5,4.6h-21.4A37.08,37.08,0,0,0,77,130.5l-1.1,1.2-1.1-1.1a37.25,37.25,0,0,0-26.3-10.9H27a19.59,19.59,0,0,1-12.4-4.6,7.28,7.28,0,0,0-9.4-.3l-2.4,1.9A7.43,7.43,0,0,0,0,122.2a7.14,7.14,0,0,0,2.4,5.7A37.28,37.28,0,0,0,27,137.4h21.6a19.59,19.59,0,0,1,18.9,14.4v.2c.1.7,1.2,4.4,8.5,4.4s8.4-3.7,8.5-4.4v-.2a19.59,19.59,0,0,1,18.9-14.4H125a37.28,37.28,0,0,0,24.6-9.5,7.42,7.42,0,0,0,2.4-5.7A7.86,7.86,0,0,0,149,116.6Z"})]}),O=(0,g.jsx)("img",{src:`${T}images/marketing/klaviyo.png`,alt:(0,a.__)("Klaviyo","woocommerce")}),I=(0,g.jsx)("img",{src:`${T}images/marketing/creative-mail-by-constant-contact.png`,alt:(0,a.__)("Creative Mail by Constant Contact","woocommerce")}),D=(0,g.jsx)("img",{src:`${T}images/marketing/jetpack-crm.svg`,alt:(0,a.__)("Jetpack CRM","woocommerce")}),V=(0,g.jsx)("img",{src:`${T}images/marketing/salesforce.jpg`,alt:(0,a.__)("Salesforce","woocommerce")}),H=(0,g.jsx)("img",{src:`${T}images/marketing/tiktok.jpg`,alt:(0,a.__)("TikTok","woocommerce")}),U=(0,g.jsx)("img",{src:`${T}images/marketing/zapier.png`,alt:(0,a.__)("Zapier","woocommerce")}),G=(0,g.jsx)("img",{src:`${T}images/marketing/vimeo.png`,alt:(0,a.__)("Vimeo","woocommerce")}),F=(0,g.jsx)("img",{src:`${T}images/marketing/trustpilot.png`,alt:(0,a.__)("Trustpilot","woocommerce")}),z=(0,g.jsx)(h.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,g.jsx)(h.Path,{d:"M3.25 12a8.75 8.75 0 1117.5 0 8.75 8.75 0 01-17.5 0zM12 4.75a7.25 7.25 0 100 14.5 7.25 7.25 0 000-14.5zm-1.338 4.877c-.314.22-.412.452-.412.623 0 .171.098.403.412.623.312.218.783.377 1.338.377.825 0 1.605.233 2.198.648.59.414 1.052 1.057 1.052 1.852 0 .795-.461 1.438-1.052 1.852-.41.286-.907.486-1.448.582v.316a.75.75 0 01-1.5 0v-.316a3.64 3.64 0 01-1.448-.582c-.59-.414-1.052-1.057-1.052-1.852a.75.75 0 011.5 0c0 .171.098.403.412.623.312.218.783.377 1.338.377s1.026-.159 1.338-.377c.314-.22.412-.452.412-.623 0-.171-.098-.403-.412-.623-.312-.218-.783-.377-1.338-.377-.825 0-1.605-.233-2.198-.648-.59-.414-1.052-1.057-1.052-1.852 0-.795.461-1.438 1.052-1.852a3.64 3.64 0 011.448-.582V7.5a.75.75 0 011.5 0v.316c.54.096 1.039.296 1.448.582.59.414 1.052 1.057 1.052 1.852a.75.75 0 01-1.5 0c0-.171-.098-.403-.412-.623-.312-.218-.783-.377-1.338-.377s-1.026.159-1.338.377z"})}),B=(0,g.jsx)(h.SVG,{xmlns:"http://www.w3.org/2000/svg",children:(0,g.jsx)(h.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M14.75 9C16.1307 9 17.25 7.88071 17.25 6.5C17.25 5.11929 16.1307 4 14.75 4C13.3693 4 12.25 5.11929 12.25 6.5C12.25 5.11929 11.1307 4 9.75 4C8.36929 4 7.25 5.11929 7.25 6.5C7.25 7.88071 8.36929 9 9.75 9H4V20L20 20V9L14.75 9ZM14.75 7.5C15.3023 7.5 15.75 7.05228 15.75 6.5C15.75 5.94772 15.3023 5.5 14.75 5.5C14.1977 5.5 13.75 5.94772 13.75 6.5V7.5H14.75ZM18.5 18.5V10.5H13V18.5H18.5ZM11.5 18.5H5.5L5.5 10.5H11.5L11.5 18.5ZM8.75 6.5C8.75 7.05228 9.19772 7.5 9.75 7.5H10.75V6.5C10.75 5.94772 10.3023 5.5 9.75 5.5C9.19772 5.5 8.75 5.94772 8.75 6.5Z"})}),$=(0,g.jsx)(h.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,g.jsx)(h.Path,{d:"M15.6 7.2H14v1.5h1.6c2 0 3.7 1.7 3.7 3.7s-1.7 3.7-3.7 3.7H14v1.5h1.6c2.8 0 5.2-2.3 5.2-5.2 0-2.9-2.3-5.2-5.2-5.2zM4.7 12.4c0-2 1.7-3.7 3.7-3.7H10V7.2H8.4c-2.9 0-5.2 2.3-5.2 5.2 0 2.9 2.3 5.2 5.2 5.2H10v-1.5H8.4c-2 0-3.7-1.7-3.7-3.7zm4.6.9h5.3v-1.5H9.3v1.5z"})}),W=(0,g.jsx)(h.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,g.jsx)(h.Path,{d:"M17.5 9a2 2 0 11-4 0 2 2 0 014 0zm-4.25 8v-2a2.75 2.75 0 00-2.75-2.75h-4A2.75 2.75 0 003.75 15v2h1.5v-2c0-.69.56-1.25 1.25-1.25h4c.69 0 1.25.56 1.25 1.25v2h1.5zm7-2v2h-1.5v-2c0-.69-.56-1.25-1.25-1.25H15v-1.5h2.5A2.75 2.75 0 0120.25 15zM8.5 11a2 2 0 100-4 2 2 0 000 4z"})}),Z=(0,g.jsx)(h.SVG,{xmlns:"http://www.w3.org/2000/svg",children:(0,g.jsx)(h.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M15 16.5H9V15h6v1.5zM15.0052 5.99481c-1.6597-1.65973-4.3507-1.65973-6.0104 0-1.65973 1.65973-1.65973 4.35069 0 6.01039.29289.2929.29289.7678 0 1.0607-.2929.2929-.76777.2929-1.06066 0-2.24552-2.2455-2.24552-5.88624 0-8.13175 2.24556-2.24551 5.88616-2.24551 8.13176 0 2.2455 2.24551 2.2455 5.88625 0 8.13175-.2929.2929-.7678.2929-1.0607 0-.2929-.2929-.2929-.7678 0-1.0607 1.6597-1.6597 1.6597-4.35066 0-6.01039zM14 19.5h-4V18h4v1.5z"})});class q extends i.Component{render(){const t=(0,f.camelCase)(this.props.product);let n=w;return t in e&&(n=e[t]),(0,g.jsx)("div",{className:m(this.props.className,"woocommerce-admin-marketing-product-icon"),children:(0,g.jsx)(p,{icon:n,size:36})})}}const K=q,Y=window.wc.components;var X=n(51609),J=n.n(X);function Q(e){return e.startsWith("{{/")?{type:"componentClose",value:e.replace(/\W/g,"")}:e.endsWith("/}}")?{type:"componentSelfClosing",value:e.replace(/\W/g,"")}:e.startsWith("{{")?{type:"componentOpen",value:e.replace(/\W/g,"")}:{type:"string",value:e}}function ee(e,t){let n,o,r=[];for(let i=0;i<e.length;i++){const a=e[i];if("string"!==a.type){if(void 0===t[a.value])throw new Error(`Invalid interpolation, missing component node: \`${a.value}\``);if("object"!=typeof t[a.value])throw new Error(`Invalid interpolation, component node must be a ReactElement or null: \`${a.value}\``);if("componentClose"===a.type)throw new Error(`Missing opening component token: \`${a.value}\``);if("componentOpen"===a.type){n=t[a.value],o=i;break}r.push(t[a.value])}else r.push(a.value)}if(n){const i=function(e,t){const n=t[e];let o=0;for(let r=e+1;r<t.length;r++){const e=t[r];if(e.value===n.value){if("componentOpen"===e.type){o++;continue}if("componentClose"===e.type){if(0===o)return r;o--}}}throw new Error("Missing closing component token `"+n.value+"`")}(o,e),a=ee(e.slice(o+1,i),t),s=(0,X.cloneElement)(n,{},a);if(r.push(s),i<e.length-1){const n=ee(e.slice(i+1),t);r=r.concat(n)}}return r=r.filter(Boolean),0===r.length?null:1===r.length?r[0]:(0,X.createElement)(X.Fragment,null,...r)}function te(e){const{mixedString:t,components:n,throwErrors:o}=e;if(!n)return t;if("object"!=typeof n){if(o)throw new Error(`Interpolation Error: unable to process \`${t}\` because components is not an object`);return t}const r=function(e){return e.split(/(\{\{\/?\s*\w+\s*\/?\}\})/g).map(Q)}(t);try{return ee(r,n)}catch(e){if(o)throw new Error(`Interpolation Error: unable to process \`${t}\` because of error \`${e.message}\``);return t}}const ne=()=>te({mixedString:(0,a.__)("Read {{link}}the WooCommerce blog{{/link}} for more tips on marketing your store","woocommerce"),components:{link:(0,g.jsx)(Y.Link,{type:"external",href:"https://woocommerce.com/blog/marketing/coupons/?utm_medium=product",target:"_blank"})}}),oe=window.wp.components,re=window.wp.url,ie=window.wp.dataControls,ae="wc/marketing",se="/wc-admin/marketing",ce={SET_INSTALLED_PLUGINS:"SET_INSTALLED_PLUGINS",SET_ACTIVATING_PLUGIN:"SET_ACTIVATING_PLUGIN",REMOVE_ACTIVATING_PLUGIN:"REMOVE_ACTIVATING_PLUGIN",SET_RECOMMENDED_PLUGINS:"SET_RECOMMENDED_PLUGINS",INSTALL_AND_ACTIVATE_RECOMMENDED_PLUGIN:"INSTALL_AND_ACTIVATE_RECOMMENDED_PLUGIN",SET_BLOG_POSTS:"SET_BLOG_POSTS",SET_MISC_RECOMMENDATIONS:"SET_MISC_RECOMMENDATIONS",SET_ERROR:"SET_ERROR"};function le(e){return{type:ce.SET_INSTALLED_PLUGINS,plugins:e}}function me(e){return{type:ce.SET_ACTIVATING_PLUGIN,pluginSlug:e}}function de(e){return{type:ce.REMOVE_ACTIVATING_PLUGIN,pluginSlug:e}}function ue(e,t){return{type:ce.SET_RECOMMENDED_PLUGINS,data:{plugins:e,category:t}}}function pe(e){return{type:ce.SET_MISC_RECOMMENDATIONS,data:{miscRecommendations:e}}}function fe(e,t){return{type:ce.SET_BLOG_POSTS,data:{posts:e,category:t}}}function he(e,t){const{createNotice:n}=(0,d.dispatch)("core/notices");n("error",t),console.log(e)}function ge(e,t){return{type:ce.SET_ERROR,category:e,error:t}}function*we(e){try{const t=yield(0,ie.apiFetch)({path:`${se}/overview/installed-plugins`});if(!t)throw new Error;yield le(t),yield de(e)}catch(e){yield he(e,(0,a.__)("There was an error loading installed extensions.","woocommerce"))}}function*ve(e){const{createNotice:t}=(0,d.dispatch)("core/notices");yield me(e);try{if(!(yield(0,ie.apiFetch)({path:se+"/overview/activate-plugin",method:"POST",data:{plugin:e}})))throw new Error;yield t("success",(0,a.__)("The extension has been successfully activated.","woocommerce")),yield we(e)}catch(t){yield he(t,(0,a.__)("There was an error trying to activate the extension.","woocommerce")),yield de(e)}return!0}function*xe(e,t){return{type:ce.INSTALL_AND_ACTIVATE_RECOMMENDED_PLUGIN,data:{pluginSlug:e,category:t}}}function Ce(e){return e.installedPlugins}function Ee(e){return e.activatingPlugins}function ye(e,t){return e.recommendedPlugins[t]||[]}function _e(e){return e.miscRecommendations}function be(e,t){return e.blogPosts[t]||[]}function Ne(e,t){return e.errors.blogPosts&&e.errors.blogPosts[t]}function*Se(e){try{const t=yield e?`&category=${e}`:"",n=yield(0,ie.apiFetch)({path:`${se}/recommended?per_page=50${t}`});if(!n)throw new Error;yield ue(n,e)}catch(e){yield he(e,(0,a.__)("There was an error loading recommended extensions.","woocommerce"))}}function*ke(){try{const e=yield(0,ie.apiFetch)({path:`${se}/misc-recommendations`});if(!e)throw new Error;yield pe(e)}catch(e){yield he(e,(0,a.__)("There was an error loading misc recommendations","woocommerce"))}}function*Te(e){try{const t=yield e?`?category=${e}`:"",n=yield(0,ie.apiFetch)({path:`${se}/knowledge-base${t}`,method:"GET"});if(!n)throw new Error;yield fe(n,e)}catch(t){yield ge(e,t)}}const{installedExtensions:je}=S("marketing",{}),Ae={installedPlugins:je,activatingPlugins:[],recommendedPlugins:{},miscRecommendations:[],blogPosts:{},errors:{}},Me=(0,d.createReduxStore)(ae,{actions:t,selectors:o,resolvers:r,controls:ie.controls,reducer:(e=Ae,t)=>{switch(t.type){case ce.SET_INSTALLED_PLUGINS:return{...e,installedPlugins:t.plugins};case ce.SET_ACTIVATING_PLUGIN:return{...e,activatingPlugins:[...e.activatingPlugins,t.pluginSlug]};case ce.REMOVE_ACTIVATING_PLUGIN:return{...e,activatingPlugins:(0,f.without)(e.activatingPlugins,t.pluginSlug)};case ce.SET_RECOMMENDED_PLUGINS:return{...e,recommendedPlugins:{...e.recommendedPlugins,[t.data.category]:t.data.plugins}};case ce.INSTALL_AND_ACTIVATE_RECOMMENDED_PLUGIN:const n=e.recommendedPlugins[t.data.category]?.filter((e=>e.product!==t.data.pluginSlug));return{...e,recommendedPlugins:{...e.recommendedPlugins,[t.data.category]:n}};case ce.SET_BLOG_POSTS:return{...e,blogPosts:{...e.blogPosts,[t.data.category]:t.data.posts}};case ce.SET_MISC_RECOMMENDATIONS:return{...e,miscRecommendations:t.data.miscRecommendations};case ce.SET_ERROR:return{...e,errors:{...e.errors,blogPosts:{...e.errors.blogPosts,[t.category]:t.error}}};default:return e}}});(0,d.register)(Me);const Le=({title:e,description:t,url:n,product:o,category:r})=>{const i="woocommerce-marketing-recommended-extensions-item",a=((e,t={})=>{const{pathname:n,search:o}=window.location,r=(0,E.getSetting)("connectNonce","");return t={"wccom-site":S("siteUrl"),"wccom-back":n+o,"wccom-woo-version":(0,E.getSetting)("wcVersion"),"wccom-connect-nonce":r,...t},(0,re.addQueryArgs)(e,t)})(n);return"coupons"===r&&"automatewoo"===o&&(o="automatewoo-alt"),(0,g.jsxs)("a",{href:a,className:i,onClick:()=>{(0,u.recordEvent)("marketing_recommended_extension",{name:e,source:S("allowMarketplaceSuggestions",!1)?"woocommerce.com":"plugin-woocommerce"})},children:[(0,g.jsx)(K,{product:o}),(0,g.jsxs)("div",{className:`${i}__text`,children:[(0,g.jsx)("h4",{children:e}),(0,g.jsx)("p",{children:t})]})]})},Re=()=>{const e="is-loading woocommerce-marketing-recommended-extensions-item";return(0,g.jsxs)("div",{className:e,"aria-hidden":"true",children:[(0,g.jsx)("div",{className:"woocommerce-admin-marketing-product-icon is-placeholder"}),(0,g.jsxs)("div",{className:`${e}__text`,children:[(0,g.jsx)("h4",{className:"is-placeholder","aria-hidden":"true"}),(0,g.jsxs)("p",{children:[(0,g.jsx)("span",{className:"is-placeholder"}),(0,g.jsx)("span",{className:"is-placeholder"}),(0,g.jsx)("span",{className:"is-placeholder"})]})]})]})},Pe=window.wc.experimental,Oe=e=>{const{title:t,description:n,children:o,className:r}=e;return(0,g.jsxs)(oe.Card,{className:m(r,"woocommerce-admin-marketing-card"),children:[(0,g.jsx)(oe.CardHeader,{children:(0,g.jsxs)("div",{children:[(0,g.jsx)(Pe.Text,{variant:"title.small",as:"p",size:"20",lineHeight:"28px",children:t}),(0,g.jsx)(Pe.Text,{variant:"subtitle.small",as:"p",className:"woocommerce-admin-marketing-card-subtitle",size:"14",lineHeight:"20px",children:n})]})}),(0,g.jsx)(oe.CardBody,{children:o})]})},Ie=(0,c.compose)((0,d.withSelect)(((e,t)=>{const{getRecommendedPlugins:n,isResolving:o}=e(ae);return{extensions:n(t.category),isLoading:o("getRecommendedPlugins",[t.category])}})),(0,d.withDispatch)((e=>{const{createNotice:t}=e("core/notices");return{createNotice:t}})))((({extensions:e,isLoading:t,title:n=(0,a.__)("Recommended extensions","woocommerce"),description:o=(0,a.__)("Great marketing requires the right tools. Take your marketing to the next level with our recommended marketing extensions.","woocommerce"),category:r})=>{if(0===e.length&&!t)return null;const i=r?`woocommerce-marketing-recommended-extensions-card__category-${r}`:"";return(0,g.jsx)(Oe,{title:n,description:o,className:m("woocommerce-marketing-recommended-extensions-card",i),children:t?(0,g.jsx)("div",{className:m("woocommerce-marketing-recommended-extensions-card__items","woocommerce-marketing-recommended-extensions-card__items--count-5"),children:[...Array(5).keys()].map((e=>(0,g.jsx)(Re,{},e)))}):(0,g.jsx)("div",{className:m("woocommerce-marketing-recommended-extensions-card__items",`woocommerce-marketing-recommended-extensions-card__items--count-${e.length}`),children:e.map((e=>(0,g.jsx)(Le,{category:r,...e},e.product)))})})}));function De(e,t){if(null==e)return{};var n={};for(var o in e)if({}.hasOwnProperty.call(e,o)){if(t.includes(o))continue;n[o]=e[o]}return n}function Ve(){return Ve=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},Ve.apply(null,arguments)}function He(e,t){return He=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},He(e,t)}function Ue(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,He(e,t)}const Ge=J().createContext(null);function Fe(e,t){var n=Object.create(null);return e&&X.Children.map(e,(function(e){return e})).forEach((function(e){n[e.key]=function(e){return t&&(0,X.isValidElement)(e)?t(e):e}(e)})),n}function ze(e,t,n){return null!=n[t]?n[t]:e.props[t]}function Be(e,t,n){var o=Fe(e.children),r=function(e,t){function n(n){return n in t?t[n]:e[n]}e=e||{},t=t||{};var o,r=Object.create(null),i=[];for(var a in e)a in t?i.length&&(r[a]=i,i=[]):i.push(a);var s={};for(var c in t){if(r[c])for(o=0;o<r[c].length;o++){var l=r[c][o];s[r[c][o]]=n(l)}s[c]=n(c)}for(o=0;o<i.length;o++)s[i[o]]=n(i[o]);return s}(t,o);return Object.keys(r).forEach((function(i){var a=r[i];if((0,X.isValidElement)(a)){var s=i in t,c=i in o,l=t[i],m=(0,X.isValidElement)(l)&&!l.props.in;!c||s&&!m?c||!s||m?c&&s&&(0,X.isValidElement)(l)&&(r[i]=(0,X.cloneElement)(a,{onExited:n.bind(null,a),in:l.props.in,exit:ze(a,"exit",e),enter:ze(a,"enter",e)})):r[i]=(0,X.cloneElement)(a,{in:!1}):r[i]=(0,X.cloneElement)(a,{onExited:n.bind(null,a),in:!0,exit:ze(a,"exit",e),enter:ze(a,"enter",e)})}})),r}var $e=Object.values||function(e){return Object.keys(e).map((function(t){return e[t]}))},We=function(e){function t(t,n){var o,r=(o=e.call(this,t,n)||this).handleExited.bind(function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(o));return o.state={contextValue:{isMounting:!0},handleExited:r,firstRender:!0},o}Ue(t,e);var n=t.prototype;return n.componentDidMount=function(){this.mounted=!0,this.setState({contextValue:{isMounting:!1}})},n.componentWillUnmount=function(){this.mounted=!1},t.getDerivedStateFromProps=function(e,t){var n,o,r=t.children,i=t.handleExited;return{children:t.firstRender?(n=e,o=i,Fe(n.children,(function(e){return(0,X.cloneElement)(e,{onExited:o.bind(null,e),in:!0,appear:ze(e,"appear",n),enter:ze(e,"enter",n),exit:ze(e,"exit",n)})}))):Be(e,r,i),firstRender:!1}},n.handleExited=function(e,t){var n=Fe(this.props.children);e.key in n||(e.props.onExited&&e.props.onExited(t),this.mounted&&this.setState((function(t){var n=Ve({},t.children);return delete n[e.key],{children:n}})))},n.render=function(){var e=this.props,t=e.component,n=e.childFactory,o=De(e,["component","childFactory"]),r=this.state.contextValue,i=$e(this.state.children).map(n);return delete o.appear,delete o.enter,delete o.exit,null===t?J().createElement(Ge.Provider,{value:r},i):J().createElement(Ge.Provider,{value:r},J().createElement(t,o,i))},t}(J().Component);We.propTypes={},We.defaultProps={component:"div",childFactory:function(e){return e}};const Ze=We;function qe(e,t){return e.replace(new RegExp("(^|\\s)"+t+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}const Ke=window.ReactDOM;var Ye=n.n(Ke);var Xe=function(e){return e.scrollTop},Je="unmounted",Qe="exited",et="entering",tt="entered",nt="exiting",ot=function(e){function t(t,n){var o;o=e.call(this,t,n)||this;var r,i=n&&!n.isMounting?t.enter:t.appear;return o.appearStatus=null,t.in?i?(r=Qe,o.appearStatus=et):r=tt:r=t.unmountOnExit||t.mountOnEnter?Je:Qe,o.state={status:r},o.nextCallback=null,o}Ue(t,e),t.getDerivedStateFromProps=function(e,t){return e.in&&t.status===Je?{status:Qe}:null};var n=t.prototype;return n.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},n.componentDidUpdate=function(e){var t=null;if(e!==this.props){var n=this.state.status;this.props.in?n!==et&&n!==tt&&(t=et):n!==et&&n!==tt||(t=nt)}this.updateStatus(!1,t)},n.componentWillUnmount=function(){this.cancelNextCallback()},n.getTimeouts=function(){var e,t,n,o=this.props.timeout;return e=t=n=o,null!=o&&"number"!=typeof o&&(e=o.exit,t=o.enter,n=void 0!==o.appear?o.appear:t),{exit:e,enter:t,appear:n}},n.updateStatus=function(e,t){if(void 0===e&&(e=!1),null!==t)if(this.cancelNextCallback(),t===et){if(this.props.unmountOnExit||this.props.mountOnEnter){var n=this.props.nodeRef?this.props.nodeRef.current:Ye().findDOMNode(this);n&&Xe(n)}this.performEnter(e)}else this.performExit();else this.props.unmountOnExit&&this.state.status===Qe&&this.setState({status:Je})},n.performEnter=function(e){var t=this,n=this.props.enter,o=this.context?this.context.isMounting:e,r=this.props.nodeRef?[o]:[Ye().findDOMNode(this),o],i=r[0],a=r[1],s=this.getTimeouts(),c=o?s.appear:s.enter;e||n?(this.props.onEnter(i,a),this.safeSetState({status:et},(function(){t.props.onEntering(i,a),t.onTransitionEnd(c,(function(){t.safeSetState({status:tt},(function(){t.props.onEntered(i,a)}))}))}))):this.safeSetState({status:tt},(function(){t.props.onEntered(i)}))},n.performExit=function(){var e=this,t=this.props.exit,n=this.getTimeouts(),o=this.props.nodeRef?void 0:Ye().findDOMNode(this);t?(this.props.onExit(o),this.safeSetState({status:nt},(function(){e.props.onExiting(o),e.onTransitionEnd(n.exit,(function(){e.safeSetState({status:Qe},(function(){e.props.onExited(o)}))}))}))):this.safeSetState({status:Qe},(function(){e.props.onExited(o)}))},n.cancelNextCallback=function(){null!==this.nextCallback&&(this.nextCallback.cancel(),this.nextCallback=null)},n.safeSetState=function(e,t){t=this.setNextCallback(t),this.setState(e,t)},n.setNextCallback=function(e){var t=this,n=!0;return this.nextCallback=function(o){n&&(n=!1,t.nextCallback=null,e(o))},this.nextCallback.cancel=function(){n=!1},this.nextCallback},n.onTransitionEnd=function(e,t){this.setNextCallback(t);var n=this.props.nodeRef?this.props.nodeRef.current:Ye().findDOMNode(this),o=null==e&&!this.props.addEndListener;if(n&&!o){if(this.props.addEndListener){var r=this.props.nodeRef?[this.nextCallback]:[n,this.nextCallback],i=r[0],a=r[1];this.props.addEndListener(i,a)}null!=e&&setTimeout(this.nextCallback,e)}else setTimeout(this.nextCallback,0)},n.render=function(){var e=this.state.status;if(e===Je)return null;var t=this.props,n=t.children,o=(t.in,t.mountOnEnter,t.unmountOnExit,t.appear,t.enter,t.exit,t.timeout,t.addEndListener,t.onEnter,t.onEntering,t.onEntered,t.onExit,t.onExiting,t.onExited,t.nodeRef,De(t,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]));return J().createElement(Ge.Provider,{value:null},"function"==typeof n?n(e,o):J().cloneElement(J().Children.only(n),o))},t}(J().Component);function rt(){}ot.contextType=Ge,ot.propTypes={},ot.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:rt,onEntering:rt,onEntered:rt,onExit:rt,onExiting:rt,onExited:rt},ot.UNMOUNTED=Je,ot.EXITED=Qe,ot.ENTERING=et,ot.ENTERED=tt,ot.EXITING=nt;const it=ot;var at=function(e,t){return e&&t&&t.split(" ").forEach((function(t){return o=t,void((n=e).classList?n.classList.remove(o):"string"==typeof n.className?n.className=qe(n.className,o):n.setAttribute("class",qe(n.className&&n.className.baseVal||"",o)));var n,o}))},st=function(e){function t(){for(var t,n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return(t=e.call.apply(e,[this].concat(o))||this).appliedClasses={appear:{},enter:{},exit:{}},t.onEnter=function(e,n){var o=t.resolveArguments(e,n),r=o[0],i=o[1];t.removeClasses(r,"exit"),t.addClass(r,i?"appear":"enter","base"),t.props.onEnter&&t.props.onEnter(e,n)},t.onEntering=function(e,n){var o=t.resolveArguments(e,n),r=o[0],i=o[1]?"appear":"enter";t.addClass(r,i,"active"),t.props.onEntering&&t.props.onEntering(e,n)},t.onEntered=function(e,n){var o=t.resolveArguments(e,n),r=o[0],i=o[1]?"appear":"enter";t.removeClasses(r,i),t.addClass(r,i,"done"),t.props.onEntered&&t.props.onEntered(e,n)},t.onExit=function(e){var n=t.resolveArguments(e)[0];t.removeClasses(n,"appear"),t.removeClasses(n,"enter"),t.addClass(n,"exit","base"),t.props.onExit&&t.props.onExit(e)},t.onExiting=function(e){var n=t.resolveArguments(e)[0];t.addClass(n,"exit","active"),t.props.onExiting&&t.props.onExiting(e)},t.onExited=function(e){var n=t.resolveArguments(e)[0];t.removeClasses(n,"exit"),t.addClass(n,"exit","done"),t.props.onExited&&t.props.onExited(e)},t.resolveArguments=function(e,n){return t.props.nodeRef?[t.props.nodeRef.current,e]:[e,n]},t.getClassNames=function(e){var n=t.props.classNames,o="string"==typeof n,r=o?(o&&n?n+"-":"")+e:n[e];return{baseClassName:r,activeClassName:o?r+"-active":n[e+"Active"],doneClassName:o?r+"-done":n[e+"Done"]}},t}Ue(t,e);var n=t.prototype;return n.addClass=function(e,t,n){var o=this.getClassNames(t)[n+"ClassName"],r=this.getClassNames("enter").doneClassName;"appear"===t&&"done"===n&&r&&(o+=" "+r),"active"===n&&e&&Xe(e),o&&(this.appliedClasses[t][n]=o,function(e,t){e&&t&&t.split(" ").forEach((function(t){return o=t,void((n=e).classList?n.classList.add(o):function(e,t){return e.classList?!!t&&e.classList.contains(t):-1!==(" "+(e.className.baseVal||e.className)+" ").indexOf(" "+t+" ")}(n,o)||("string"==typeof n.className?n.className=n.className+" "+o:n.setAttribute("class",(n.className&&n.className.baseVal||"")+" "+o)));var n,o}))}(e,o))},n.removeClasses=function(e,t){var n=this.appliedClasses[t],o=n.base,r=n.active,i=n.done;this.appliedClasses[t]={},o&&at(e,o),r&&at(e,r),i&&at(e,i)},n.render=function(){var e=this.props,t=(e.classNames,De(e,["classNames"]));return J().createElement(it,Ve({},t,{onEnter:this.onEnter,onEntered:this.onEntered,onEntering:this.onEntering,onExit:this.onExit,onExiting:this.onExiting,onExited:this.onExited}))},t}(J().Component);st.defaultProps={classNames:""},st.propTypes={};const ct=st,lt=({children:e,animationKey:t,animate:n})=>{const[o,r]=(0,i.useState)(null),a=(0,i.useRef)(),s=m("woocommerce-marketing-slider",n&&`animate-${n}`),c={};o&&(c.height=o);const l=(0,f.debounce)((()=>{const e=a.current.querySelector(".woocommerce-marketing-slider__slide");r(e.clientHeight)}),50);return(0,i.useEffect)((()=>(window.addEventListener("resize",l),()=>{window.removeEventListener("resize",l)})),[]),(0,g.jsx)("div",{className:s,ref:a,style:c,children:(0,g.jsx)(Ze,{children:(0,g.jsx)(ct,{timeout:320,classNames:"slide",onEnter:()=>{const e=a.current.querySelector(".slide-enter");r(e.clientHeight)},children:(0,g.jsx)("div",{className:"woocommerce-marketing-slider__slide",children:e})},t)})})},mt=e=>{const t="woocommerce-marketing-knowledgebase-card__post";return(0,g.jsxs)("div",{className:`is-loading ${t}`,"aria-hidden":"true",children:[(0,g.jsx)("div",{className:`${t}-img is-placeholder`}),(0,g.jsxs)("div",{className:`${t}-text`,children:[(0,g.jsx)("h3",{className:"is-placeholder","aria-hidden":"true"}),(0,g.jsx)("p",{className:`${t}-meta is-placeholder`})]})]},e)},dt=(0,c.compose)((0,d.withSelect)(((e,t)=>{const{getBlogPosts:n,getBlogPostsError:o,isResolving:r}=e(ae);return{posts:n(t.category),isLoading:r("getBlogPosts",[t.category]),error:o(t.category)}})),(0,d.withDispatch)((e=>{const{createNotice:t}=e("core/notices");return{createNotice:t}})))((({posts:e,isLoading:t,error:n,title:o=(0,a.__)("WooCommerce knowledge base","woocommerce"),description:r=(0,a.__)("Learn the ins and outs of successful marketing from the experts at WooCommerce.","woocommerce"),category:s})=>{const[c,l]=(0,i.useState)(1),[d,p]=(0,i.useState)(null),f=s?`woocommerce-marketing-knowledgebase-card__category-${s}`:"";return(0,g.jsx)(Oe,{title:o,description:r,className:m("woocommerce-marketing-knowledgebase-card",f),children:t?(0,g.jsx)("div",{className:"woocommerce-marketing-knowledgebase-card__posts",children:(0,g.jsxs)("div",{className:"woocommerce-marketing-knowledgebase-card__page",children:[(0,g.jsx)(mt,{}),(0,g.jsx)(mt,{})]})}):n?(()=>{const e=(0,a.__)("Oops, our posts aren't loading right now","woocommerce");return(0,g.jsx)(Y.EmptyContent,{title:e,message:(0,g.jsx)(ne,{}),illustration:"",actionLabel:""})})():0===e.length?(()=>{const e=(0,a.__)("No posts yet","woocommerce");return(0,g.jsx)(Y.EmptyContent,{title:e,message:(0,g.jsx)(ne,{}),illustration:"",actionLabel:""})})():(0,g.jsxs)("div",{className:"woocommerce-marketing-knowledgebase-card__posts",children:[(0,g.jsx)(lt,{animationKey:c,animate:d,children:(()=>{const t=e.slice(2*(c-1),2*(c-1)+2),n=m("woocommerce-marketing-knowledgebase-card__page",{"page-with-single-post":1===t.length}),o=t.map(((e,t)=>(0,g.jsxs)("a",{className:"woocommerce-marketing-knowledgebase-card__post",href:e.link,onClick:()=>{(e=>{(0,u.recordEvent)("marketing_knowledge_article",{title:e.title})})(e)},target:"_blank",rel:"noopener noreferrer",children:[!!e.image&&(0,g.jsx)("div",{className:"woocommerce-marketing-knowledgebase-card__post-img",children:(0,g.jsx)("img",{src:e.image,alt:""})}),(0,g.jsxs)("div",{className:"woocommerce-marketing-knowledgebase-card__post-text",children:[(0,g.jsx)("h3",{children:e.title}),(0,g.jsxs)("p",{className:"woocommerce-marketing-knowledgebase-card__post-meta",children:[(0,a.__)("By","woocommerce")+" ",e.author_name,!!e.author_avatar&&(0,g.jsx)("img",{src:e.author_avatar.replace("s=96","s=32"),className:"woocommerce-gravatar",alt:"",width:"16",height:"16"})]})]})]},t)));return(0,g.jsx)("div",{className:n,children:o})})()}),(0,g.jsx)(Y.Pagination,{page:c,perPage:2,total:e.length,onPageChange:e=>{let t;e>c?(t="left",(0,u.recordEvent)("marketing_knowledge_carousel",{direction:"forward",page:e})):(t="right",(0,u.recordEvent)("marketing_knowledge_carousel",{direction:"back",page:e})),l(e),p(t)},showPagePicker:!1,showPerPagePicker:!1,showPageArrowsLabel:!1})]})})})),ut=(0,g.jsx)(h.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,g.jsx)(h.Path,{d:"M12 3.2c-4.8 0-8.8 3.9-8.8 8.8 0 4.8 3.9 8.8 8.8 8.8 4.8 0 8.8-3.9 8.8-8.8 0-4.8-4-8.8-8.8-8.8zm0 16c-4 0-7.2-3.3-7.2-7.2C4.8 8 8 4.8 12 4.8s7.2 3.3 7.2 7.2c0 4-3.2 7.2-7.2 7.2zM11 17h2v-6h-2v6zm0-8h2V7h-2v2z"})}),pt=(0,g.jsx)(h.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,g.jsx)(h.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"})}),ft=(0,g.jsx)(h.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,g.jsx)(h.Path,{fillRule:"evenodd",d:"M6.5 8a1.5 1.5 0 103 0 1.5 1.5 0 00-3 0zM8 5a3 3 0 100 6 3 3 0 000-6zm6.5 11a1.5 1.5 0 103 0 1.5 1.5 0 00-3 0zm1.5-3a3 3 0 100 6 3 3 0 000-6zM5.47 17.41a.75.75 0 001.06 1.06L18.47 6.53a.75.75 0 10-1.06-1.06L5.47 17.41z",clipRule:"evenodd"})}),ht=(0,g.jsx)(h.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,g.jsx)(h.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})});var gt=n(13240);const wt=["a","b","em","i","strong","p","br"],vt=["target","href","rel","name","download"],xt=e=>({__html:(0,gt.sanitize)(e,{ALLOWED_TAGS:wt,ALLOWED_ATTR:vt})}),Ct={info:ut,check:pt,percent:ft};function Et(e){const{id:t,description:n,children:o,icon:r,isDismissible:a=!0,variant:s="info",className:c,onClose:l,onLoad:d}=e,[u,f]=(0,i.useState)("true"!==localStorage.getItem(`wc-marketplaceNoticeClosed-${t}`));if((0,i.useEffect)((()=>{u&&"function"==typeof d&&d()}),[u]),!u)return null;const h=m("woocommerce-marketplace__notice",`woocommerce-marketplace__notice--${s}`,{"is-dismissible":a},c),w=Ct[r||"info"],v=m("woocommerce-marketplace__notice-icon",`woocommerce-marketplace__notice-icon--${s}`);return(0,g.jsxs)("div",{className:h,children:[r&&(0,g.jsx)("span",{className:v,children:(0,g.jsx)(p,{icon:w})}),(0,g.jsxs)("div",{className:"woocommerce-marketplace__notice-content",children:[(0,g.jsx)("p",{className:"woocommerce-marketplace__notice-description",dangerouslySetInnerHTML:xt(n)}),o&&(0,g.jsx)("div",{className:"woocommerce-marketplace__notice-children",children:o})]}),a&&(0,g.jsx)("button",{className:"woocommerce-marketplace__notice-close","aria-label":"Close",onClick:()=>{f(!1),localStorage.setItem(`wc-marketplaceNoticeClosed-${t}`,"true"),"function"==typeof l&&l()},children:(0,g.jsx)(p,{icon:ht})})]})}const yt={percent:function(){return(0,g.jsxs)("svg",{width:"72",height:"60",viewBox:"0 0 72 60",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,g.jsxs)("g",{clipPath:"url(#clip0_4074_10418)",children:[(0,g.jsx)("path",{d:"M68.5301 33.3144C68.0263 32.1006 66.3348 32.344 65.8443 31.1636C65.3538 29.9832 66.7251 28.9562 66.2213 27.7458C65.7175 26.5354 64.0259 26.7755 63.5355 25.5951C63.045 24.4147 64.4163 23.3877 63.9125 22.1773C63.4087 20.9669 61.7171 21.207 61.2267 20.0266C60.7362 18.8462 62.1075 17.8192 61.6037 16.6088C61.0999 15.395 59.4083 15.6385 58.9179 14.4581C58.4274 13.2777 59.7987 12.2507 59.2949 11.0403C58.7911 9.82652 57.0995 10.0699 56.6091 8.88955C56.1186 7.70915 57.4899 6.68214 56.9861 5.47174C56.4823 4.26134 54.7907 4.50142 54.3003 3.32102C53.8465 2.22733 55.0476 1.11696 54.8274 -0.00341797L0 22.5941C0.5038 23.8079 2.19537 23.5644 2.68582 24.7448C3.17627 25.9252 1.805 26.9522 2.3088 28.1626C2.8126 29.373 4.50417 29.133 4.99462 30.3134C5.48508 31.4937 4.11381 32.5208 4.61761 33.7312C5.12141 34.9416 6.81297 34.7015 7.30343 35.8819C7.79388 37.0623 6.42261 38.0893 6.92641 39.2997C7.43021 40.5134 9.12178 40.27 9.61223 41.4504C10.1027 42.6308 8.73142 43.6578 9.23522 44.8682C9.73902 46.0786 11.4306 45.8385 11.921 47.0189C12.4115 48.1993 11.0402 49.2263 11.544 50.4367C12.0478 51.6471 13.7394 51.4071 14.2298 52.5874C14.6836 53.6811 13.4825 54.7915 13.7027 55.9119L28.1928 49.9232L68.5368 33.3177L68.5301 33.3144Z",fill:"#720EEC"}),(0,g.jsx)("path",{d:"M13.696 55.912L28.1861 49.9234L52.3851 39.9634H7.46021C8.17086 40.4802 9.23852 40.5569 9.60886 41.4539C10.0993 42.6343 8.72805 43.6613 9.23185 44.8717C9.73565 46.0821 11.4272 45.842 11.9177 47.0224C12.4081 48.2028 11.0368 49.2298 11.5406 50.4402C12.0444 51.6506 13.736 51.4105 14.2265 52.5909C14.6802 53.6846 13.4791 54.795 13.6993 55.9154L13.696 55.912Z",fill:"#3C087E"}),(0,g.jsx)("path",{d:"M63.8523 41.9907C63.8523 37.4925 67.499 33.848 71.9998 33.848V23.988H17.873V33.848C22.3739 33.848 26.0206 37.4925 26.0206 41.9907C26.0206 46.4889 22.3739 50.1334 17.873 50.1334V59.9934H71.9998V50.1334C67.499 50.1334 63.8523 46.4889 63.8523 41.9907Z",fill:"#D1C1FF"}),(0,g.jsx)("path",{d:"M35.2527 37.676C35.2527 35.2051 37.0143 33.2878 39.6968 33.2878C42.3793 33.2878 44.1643 35.2051 44.1643 37.676C44.1643 40.1468 42.4026 42.0107 39.6968 42.0107C36.991 42.0107 35.2527 40.1201 35.2527 37.676ZM41.7954 37.676C41.7954 36.2288 40.9046 35.3385 39.6935 35.3385C38.4823 35.3385 37.6182 36.2288 37.6182 37.676C37.6182 39.1231 38.509 39.9601 39.6935 39.9601C40.8779 39.9601 41.7954 39.0664 41.7954 37.676ZM37.9852 51.0704L49.1789 33.5513H51.1774L39.9537 51.0704H37.9819H37.9852ZM44.8983 47.0524C44.8983 44.5849 46.6566 42.641 49.3391 42.641C52.0215 42.641 53.8065 44.5849 53.8065 47.0524C53.8065 49.5199 52.0182 51.3872 49.3391 51.3872C46.6599 51.3872 44.8983 49.4966 44.8983 47.0524ZM51.441 47.0524C51.441 45.6053 50.5468 44.715 49.3357 44.715C48.1246 44.715 47.2605 45.6053 47.2605 47.0524C47.2605 48.4996 48.1279 49.3365 49.3357 49.3365C50.5435 49.3365 51.441 48.4696 51.441 47.0524Z",fill:"#720EEC"})]}),(0,g.jsx)("defs",{children:(0,g.jsx)("clipPath",{id:"clip0_4074_10418",children:(0,g.jsx)("rect",{width:"72",height:"60",fill:"white"})})})]})}},_t=({promotion:e})=>{var t,n;const o=window.location.pathname+window.location.search,r=()=>JSON.parse(localStorage.getItem("wc-marketplaceDismissedPromos")||"[]"),[s,c]=(0,i.useState)(!r().includes(o));if((0,i.useEffect)((()=>{s&&(0,u.recordEvent)("marketplace_promotion_viewed",{path:o,format:"promo-card"})}),[s]),!s)return null;const l="promo-card"+(e.style?` ${e.style}`:""),m=(0,g.jsxs)("div",{className:"promo-content",children:[(0,g.jsx)("h2",{className:"promo-title",children:e.title?.en_US}),(0,g.jsx)("div",{className:"promo-text",dangerouslySetInnerHTML:xt(e.content?.en_US)})]}),d=(0,g.jsxs)("div",{className:"promo-links",children:[(0,g.jsx)(oe.Button,{className:"promo-cta",href:null!==(t=e.cta_link)&&void 0!==t?t:"",onClick:()=>((0,u.recordEvent)("marketplace_promotion_actioned",{path:o,target_uri:e.cta_link,format:"promo-card"}),!0),children:null!==(n=e.cta_label?.en_US)&&void 0!==n?n:""}),(0,g.jsx)(oe.Button,{className:"promo-cta-link",onClick:()=>{c(!1),localStorage.setItem("wc-marketplaceDismissedPromos",JSON.stringify(r().concat(o))),(0,u.recordEvent)("marketplace_promotion_dismissed",{path:o,format:"promo-card"})},children:(0,a.__)("Dismiss","woocommerce")})]});function p(){if(e.icon&&Object.hasOwn(yt,e.icon)){const t=yt[e.icon];return t?(0,g.jsx)("div",{className:"promo-image",children:(0,i.createElement)(t)}):null}return null}return(0,g.jsx)("div",{className:l,children:"has-background"===e?.style?(0,g.jsxs)(g.Fragment,{children:[(0,g.jsxs)("div",{className:"promo-content-links",children:[m,d]}),p()]}):(0,g.jsxs)(g.Fragment,{children:[(0,g.jsxs)("div",{className:"promo-content-image",children:[m,p()]}),d]})})},bt=({format:e})=>{var t;if(!window?.wcMarketplace?.promotions||!Array.isArray(window?.wcMarketplace?.promotions))return null;const n=(null!==(t=window?.wcMarketplace?.promotions)&&void 0!==t?t:[]).filter((t=>t.format===e)),o=new URLSearchParams(window.location.search),r=o.get("page"),i=Date.now(),a=decodeURIComponent(o.get("path")||""),s=o.get("tab"),c=window.location.pathname+window.location.search,l=()=>{(0,u.recordEvent)("marketplace_promotion_viewed",{path:c,format:e})},m=()=>{(0,u.recordEvent)("marketplace_promotion_dismissed",{path:c,format:e})};return(0,g.jsx)(g.Fragment,{children:n.map(((e,t)=>{if(!e.pages)return null;if(!e.pages.some((e=>{if(e.pathname)return e.pathname===c;if(!e.path)return!1;const t=e=>e.startsWith("/")?e:`/${e}`,n=t(e.path),o=t(a);return e.page===r&&n===o&&(e.tab?s:!s)})))return null;const n=new Date(e.date_from_gmt).getTime(),o=new Date(e.date_to_gmt).getTime();return i<n||i>o?null:"promo-card"===e.format?(0,g.jsx)(_t,{promotion:e},t):"notice"===e.format&&e?.content?(0,g.jsx)(Et,{id:null!==(d=e.menu_item_id)&&void 0!==d?d:`promotion-${t}`,description:e.content[k.userLocale]||e.content.en_US,variant:e.style?e.style:"info",icon:e?.icon||"",isDismissible:e.is_dismissible||!1,onLoad:l,onClose:m},t):null;var d}))})},Nt=()=>{const{currentUserCan:e}=(0,s.useUser)(),t=!!S("allowMarketplaceSuggestions",!1),n=t&&e("install_plugins");return(0,g.jsxs)("div",{className:"woocommerce-marketing-coupons",children:[(0,g.jsx)(bt,{format:"promo-card"}),n&&(0,g.jsx)(Ie,{title:(0,a.__)("Recommended coupon extensions","woocommerce"),description:(0,a.__)("Take your coupon marketing to the next level with our recommended coupon extensions.","woocommerce"),category:"coupons"}),t&&(0,g.jsx)(dt,{category:"coupons",description:(0,a.__)("Learn the ins and outs of successful coupon marketing from the experts at WooCommerce.","woocommerce")})]})},St=document.getElementById("posts-filter");if(St){const e=document.createElement("div");e.setAttribute("id","coupon-root"),(0,i.createRoot)(St.parentNode.appendChild(e)).render((0,g.jsx)(Nt,{}))}})(),(window.wc=window.wc||{}).marketingCoupons={}})();