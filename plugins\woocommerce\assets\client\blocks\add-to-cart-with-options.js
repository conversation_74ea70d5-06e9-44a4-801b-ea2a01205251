(()=>{var e,t,o,r={9927:(e,t,o)=>{"use strict";var r=o(6012);const s=window.wp.plugins,i=window.wp.blocks,n=window.wp.data,c=window.wc.wcTypes;class a{blocks=new Map;initialized=!1;attemptedRegisteredBlocks=new Set;constructor(){this.initializeSubscriptions()}static getInstance(){return a.instance||(a.instance=new a),a.instance}parseTemplateId(e){const t=(0,c.isNumber)(e)?void 0:e;return t?.split("//")[1]}initializeSubscriptions(){if(this.initialized)return;const e=(0,n.subscribe)((()=>{const t=(0,n.select)("core/edit-site"),o=(0,n.select)("core/edit-post");if(t||o)if(t){const o=t.getEditedPostId();e(),this.currentTemplateId="string"==typeof o?this.parseTemplateId(o):void 0,(0,n.subscribe)((()=>{const e=this.currentTemplateId;this.currentTemplateId=this.parseTemplateId(t.getEditedPostId()),e!==this.currentTemplateId&&this.handleTemplateChange(e)}),"core/edit-site"),this.initialized=!0}else o&&(e(),this.blocks.forEach((e=>{if(e.isAvailableOnPostEditor){const t=e.variationName||e.blockName;this.hasAttemptedRegistration(t)||this.registerBlock(e)}})),this.initialized=!0)}))}handleTemplateChange(e){(this.currentTemplateId?.includes("single-product")||e?.includes("single-product"))&&this.blocks.forEach((e=>{this.unregisterBlock(e),this.registerBlock(e)}))}hasAttemptedRegistration(e){return this.attemptedRegisteredBlocks.has(e)}unregisterBlock(e){const{blockName:t,isVariationBlock:o,variationName:r}=e;try{o&&r?((0,i.unregisterBlockVariation)(t,r),this.attemptedRegisteredBlocks.delete(r)):((0,i.unregisterBlockType)(t),this.attemptedRegisteredBlocks.delete(t))}catch(e){console.debug(`Failed to unregister block ${t}:`,e)}}registerBlock(e){const{blockName:t,settings:o,isVariationBlock:r,variationName:s,isAvailableOnPostEditor:a}=e;try{const e=s||t;if(this.hasAttemptedRegistration(e))return;const l=(0,n.select)("core/edit-site");if(!l&&!a)return;if(r)(0,i.registerBlockVariation)(t,o);else{const e=(0,c.isEmpty)(o?.ancestor)?["woocommerce/single-product"]:o?.ancestor,r=l&&this.currentTemplateId?.includes("single-product");(0,i.registerBlockType)(t,{...o,ancestor:r?void 0:e})}this.attemptedRegisteredBlocks.add(e)}catch(e){console.error(`Failed to register block ${t}:`,e)}}registerBlockConfig(e){const t=e.variationName||e.blockName;this.blocks.set(t,e),this.registerBlock(e)}}var l=o(7723);const d=window.wp.components;var p=o(1244),u=o.n(p);u()("wc-admin:tracks:stats");const w=u()("wc-admin:tracks");function m(e,t){if(w("recordevent %s %o","wcadmin_"+e,t,{_tqk:window._tkq,shouldRecord:!!window._tkq&&!!window.wcTracks&&!!window.wcTracks.isEnabled}),!window.wcTracks||"function"!=typeof window.wcTracks.recordEvent)return!1;window.wcTracks.recordEvent(e,t)}const h=window.wp.editor,g="woocommerce/product-type-template-state",k="SWITCH_PRODUCT_TYPE",b="SET_PRODUCT_TYPES",y="REGISTER_LISTENER",f="UNREGISTER_LISTENER",_=window.wc.wcSettings,T=(0,_.getSetting)("productTypes",{}),v=Object.keys(T).map((e=>({slug:e,label:T[e]}))),x={productTypes:{list:v,current:v[0]?.slug},listeners:[]},j={switchProductType:e=>({type:k,current:e}),setProductTypes:e=>({type:b,productTypes:e}),registerListener:e=>({type:y,listener:e}),unregisterListener:e=>({type:f,listener:e})},S=(0,n.createReduxStore)(g,{reducer:(e=x,t)=>{switch(t.type){case b:return{...e,productTypes:{...e.productTypes,list:t.productTypes||[]}};case k:return{...e,productTypes:{...e.productTypes,current:t.current}};case y:return{...e,listeners:[...e.listeners,t.listener||""]};case f:return{...e,listeners:e.listeners.filter((e=>e!==t.listener))};default:return e}},actions:j,selectors:{getProductTypes:e=>e.productTypes.list,getCurrentProductType:e=>e.productTypes.list.find((t=>t.slug===e.productTypes.current)),getRegisteredListeners:e=>e.listeners}});function C(){const{productTypes:e,current:t,registeredListeners:o}=(0,n.useSelect)((e=>{const{getProductTypes:t,getCurrentProductType:o,getRegisteredListeners:r}=e(S);return{productTypes:t(),current:o(),registeredListeners:r()}}),[]),{switchProductType:r,registerListener:s,unregisterListener:i}=(0,n.useDispatch)(S);return{productTypes:e,current:t,set:r,registeredListeners:o,registerListener:s,unregisterListener:i}}(0,n.select)(g)||(0,n.register)(S);var B=o(790);function P(){const{productTypes:e,current:t,set:o}=C();return(0,B.jsx)(d.SelectControl,{label:(0,l.__)("Type switcher","woocommerce"),value:t?.slug,options:e.map((e=>({label:e.label,value:e.slug}))),onChange:e=>{o(e),m("blocks_add_to_cart_with_options_product_type_switched",{context:"inspector",from:t?.slug,to:e})},help:(0,l.__)("Switch product type to see how the template adapts to each one.","woocommerce")})}const O=JSON.parse('{"name":"woocommerce/add-to-cart-with-options","title":"Add to Cart with Options (Experimental)","description":"Create an \\"Add To Cart\\" composition by using blocks","category":"woocommerce-product-elements","attributes":{"isDescendantOfAddToCartWithOptions":{"type":"boolean","default":true}},"usesContext":["postId"],"providesContext":{"woocommerce/isDescendantOfAddToCartWithOptions":"isDescendantOfAddToCartWithOptions"},"textdomain":"woocommerce","supports":{"interactivity":true},"apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json","viewScriptModule":"woocommerce/add-to-cart-with-options","style":"file:../woocommerce/add-to-cart-with-options-style.css","editorStyle":"file:../woocommerce/add-to-cart-with-options-editor.css"}');var E=o(6087),I=o(4921);o(2405);const N=({tag:e="div",width:t="100%",height:o="8px",maxWidth:r="",className:s="",borderRadius:i="",isStatic:n=!1})=>(0,B.jsx)(e,{className:(0,I.A)("wc-block-components-skeleton__element",{"wc-block-components-skeleton__element--static":n},s),"aria-hidden":"true",style:{width:t,height:o,borderRadius:i,maxWidth:r}}),R=({isStatic:e=!1})=>(0,B.jsxs)("div",{className:"wc-block-components-skeleton",children:[(0,B.jsx)(N,{height:"16px",isStatic:e}),(0,B.jsx)(N,{height:"16px",isStatic:e}),(0,B.jsx)(N,{height:"16px",width:"80%",isStatic:e})]}),A=window.wc.wcBlocksSharedContext,L=window.wp.blockEditor;var D=o(5573);const M=(0,B.jsxs)(D.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[(0,B.jsx)("path",{fill:"none",d:"M0 0h24v24H0V0z"}),(0,B.jsx)("path",{d:"M12 6a9.77 9.77 0 0 1 8.82 5.5C19.17 14.87 15.79 17 12 17s-7.17-2.13-8.82-5.5A9.77 9.77 0 0 1 12 6m0-2C7 4 2.73 7.11 1 11.5 2.73 15.89 7 19 12 19s9.27-3.11 11-7.5C21.27 7.11 17 4 12 4zm0 5a2.5 2.5 0 0 1 0 5 2.5 2.5 0 0 1 0-5m0-2c-2.48 0-4.5 2.02-4.5 4.5S9.52 16 12 16s4.5-2.02 4.5-4.5S14.48 7 12 7z"})]});function z(){const{current:e,productTypes:t,set:o}=C(),{product:r}=(0,A.useProductDataContext)();return r?.id||t?.length<2?null:(0,B.jsx)(d.ToolbarGroup,{children:(0,B.jsx)(d.ToolbarDropdownMenu,{icon:(0,B.jsx)(d.Icon,{icon:M}),text:e?.label||(0,l.__)("Switch product type","woocommerce"),label:(0,l.__)("Switch product type","woocommerce"),value:e?.slug,controls:t.map((t=>({title:t.label,onClick:()=>{o(t.slug),e?.slug!==t.slug&&m("blocks_add_to_cart_with_options_product_type_switched",{context:"toolbar",from:e?.slug,to:t.slug})}})))})})}function W({children:e,className:t,actionLabel:o,onActionClick:r,...s}){return(0,B.jsx)(d.Notice,{...s,className:(0,I.$)("wc-block-editor-components-upgrade-downgrade-notice",t),actions:[{label:o,onClick:r,noDefaultClasses:!0,variant:"link"}],children:(0,B.jsx)("div",{className:"wc-block-editor-components-upgrade-downgrade-notice__text",children:e})})}o(9969);const V=({blocks:e,findCondition:t})=>{for(const o of e){if(t(o))return o;if(o.innerBlocks){const e=V({blocks:o.innerBlocks,findCondition:t});if(e)return e}}},q=({blockClientId:e})=>{const t=(0,l.__)("Switch back to the classic Add to Cart with Options block.","woocommerce"),o=(0,l.__)("Switch back","woocommerce");return(0,B.jsx)(W,{isDismissible:!1,actionLabel:o,onActionClick:async()=>{const t=await(e=>{const t=(0,n.select)("core/block-editor").getBlocks(),o=V({blocks:t,findCondition:t=>t.name===O.name&&t.clientId===e});if(!o)return!1;const r=(0,i.createBlock)("woocommerce/add-to-cart-form",{quantitySelectorStyle:"input"});return(0,n.dispatch)("core/block-editor").replaceBlock(o.clientId,r),!0})(e);t&&m("blocks_add_to_cart_with_options_migration",{transform_to:"legacy"})},children:t})},$=window.wp.coreData,F=({blockProps:e,templatePartId:t})=>{const[o,r,s]=(0,$.useEntityBlockEditor)("postType","wp_template_part",{id:t}),{isLoading:i}=(0,n.useSelect)((e=>{const{hasFinishedResolution:o}=e($.store);return{isLoading:!(t&&o("getEditedEntityRecord",["postType","wp_template_part",t]))}}),[t]),c=(0,L.useInnerBlocksProps)(e,{value:o,onInput:r,onChange:s,renderAppender:()=>i||0!==o.length?null:L.InnerBlocks.ButtonBlockAppender});return i?(0,B.jsx)("div",{...e,children:(0,B.jsx)(d.Spinner,{})}):(0,B.jsx)("div",{...c})},G=({productType:e})=>{const t=(0,_.getSetting)("addToCartWithOptionsTemplatePartIds",{}),o=t?.[e],r=(0,L.useBlockProps)();return o?(0,B.jsx)(F,{blockProps:r,templatePartId:o}):(0,B.jsx)("div",{...r,children:(0,B.jsx)(d.Spinner,{})})},J=e=>{const{product:t}=(0,A.useProductDataContext)(),o=(0,L.useBlockProps)(),r=o?.id,{current:s,registerListener:i,unregisterListener:n}=C();(0,E.useEffect)((()=>(i(r),()=>{n(r)})),[r,i,n]);const c=0===t.id?s?.slug:t.type,a=c&&["simple","variable","external","grouped"].includes(c);return(0,B.jsxs)(B.Fragment,{children:[(0,B.jsx)(L.InspectorControls,{children:(0,B.jsx)(q,{blockClientId:e?.clientId})}),(0,B.jsx)(L.BlockControls,{children:(0,B.jsx)(z,{})}),a?(0,B.jsx)(G,{productType:c}):(0,B.jsxs)("div",{...o,children:[(0,B.jsx)("div",{className:"wp-block-woocommerce-add-to-cart-with-options__skeleton-wrapper",children:(0,B.jsx)(R,{})}),(0,B.jsx)(d.Disabled,{children:(0,B.jsx)("button",{className:`alt wp-element-button ${c}_add_to_cart_button`,children:(0,l.__)("Add to cart","woocommerce")})})]})]})},U=(0,_.getSettingWithCoercion)("isBlockifiedAddToCart",!1,c.isBoolean),H=(0,_.getSetting)("isBlockTheme"),Y=U&&H;if(o(9959),Y){const e="document-settings-template-selector-pane";(0,s.getPlugin)(e)||(0,s.registerPlugin)(e,{render:function(){const{slug:e,type:t}=(0,n.useSelect)((e=>{const{slug:t,type:o}=e("core/editor").getCurrentPost();return{slug:t,type:o}}),[]),{registeredListeners:o}=C();return"wp_template"===t&&"single-product"===e&&o.length>0?(0,B.jsx)(h.PluginDocumentSettingPanel,{name:"woocommerce/product-type-selector",title:(0,l.__)("Product Type","woocommerce"),children:(0,B.jsx)(P,{})}):null}}),(e=>{const t=e.name;if(!t)return void console.error("registerProductBlockType: Block name is required for registration");const o=(({name:e,...t})=>t)(e),{isVariationBlock:r,variationName:s,isAvailableOnPostEditor:i,...n}={...o,...{isAvailableOnPostEditor:!0}||{}},c={blockName:t,settings:{...n},isVariationBlock:null!=r&&r,variationName:null!=s?s:void 0,isAvailableOnPostEditor:null!=i&&i};a.getInstance().registerBlockConfig(c)})({...O,icon:{src:({size:e})=>(0,B.jsx)("span",{className:"wp-block-woocommerce-add-to-cart-with-options__block-icon",style:{height:e,width:e},children:r.A})},edit:J,save:()=>null,ancestor:["woocommerce/single-product"]})}},9959:()=>{},2405:()=>{},9969:()=>{},1609:e=>{"use strict";e.exports=window.React},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},6087:e=>{"use strict";e.exports=window.wp.element},7723:e=>{"use strict";e.exports=window.wp.i18n},5573:e=>{"use strict";e.exports=window.wp.primitives}},s={};function i(e){var t=s[e];if(void 0!==t)return t.exports;var o=s[e]={exports:{}};return r[e].call(o.exports,o,o.exports,i),o.exports}i.m=r,e=[],i.O=(t,o,r,s)=>{if(!o){var n=1/0;for(d=0;d<e.length;d++){for(var[o,r,s]=e[d],c=!0,a=0;a<o.length;a++)(!1&s||n>=s)&&Object.keys(i.O).every((e=>i.O[e](o[a])))?o.splice(a--,1):(c=!1,s<n&&(n=s));if(c){e.splice(d--,1);var l=r();void 0!==l&&(t=l)}}return t}s=s||0;for(var d=e.length;d>0&&e[d-1][2]>s;d--)e[d]=e[d-1];e[d]=[o,r,s]},i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},o=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,i.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var s=Object.create(null);i.r(s);var n={};t=t||[null,o({}),o([]),o(o)];for(var c=2&r&&e;"object"==typeof c&&!~t.indexOf(c);c=o(c))Object.getOwnPropertyNames(c).forEach((t=>n[t]=()=>e[t]));return n.default=()=>e,i.d(s,n),s},i.d=(e,t)=>{for(var o in t)i.o(t,o)&&!i.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.j=7435,(()=>{var e={7435:0};i.O.j=t=>0===e[t];var t=(t,o)=>{var r,s,[n,c,a]=o,l=0;if(n.some((t=>0!==e[t]))){for(r in c)i.o(c,r)&&(i.m[r]=c[r]);if(a)var d=a(i)}for(t&&t(o);l<n.length;l++)s=n[l],i.o(e,s)&&e[s]&&e[s][0](),e[s]=0;return i.O(d)},o=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))})();var n=i.O(void 0,[94],(()=>i(9927)));n=i.O(n),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["add-to-cart-with-options"]=n})();