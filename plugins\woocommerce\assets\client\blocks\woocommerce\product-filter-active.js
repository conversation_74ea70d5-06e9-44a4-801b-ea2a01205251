import*as e from"@wordpress/interactivity";var t={d:(e,o)=>{for(var r in o)t.o(o,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:o[r]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const o=(a={getConfig:()=>e.getConfig,getContext:()=>e.getContext,store:()=>e.store},n={},t.d(n,a),n),r={state:{get removeActiveFilterLabel(){const{item:e}=(0,o.getContext)(),{removeLabelTemplate:t}=(0,o.getConfig)();return t.replace("{{label}}",e.activeLabel)},get hasActiveFilters(){const{activeFilters:e}=(0,o.getContext)();return e.length>0}},actions:{removeAllActiveFilters:()=>{(0,o.getContext)().activeFilters=[],i.navigate()},removeActiveFilter:()=>{const{item:e}=(0,o.getContext)();i.removeActiveFiltersBy((t=>t.value===e.value&&t.type===e.type)),i.navigate()}}},{actions:i}=(0,o.store)("woocommerce/product-filters",r);var a,n;