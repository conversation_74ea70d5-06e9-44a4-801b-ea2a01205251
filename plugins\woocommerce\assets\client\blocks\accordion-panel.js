(()=>{"use strict";var e,t,r,o={424:(e,t,r)=>{r.r(t),r.d(t,{metadata:()=>i,name:()=>p,settings:()=>u});const o=window.wp.components,a=window.wp.blocks,l=window.wp.blockEditor;var n=r(4921),s=r(790);const i=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"woocommerce/accordion-panel","title":"Accordion Panel","category":"woocommerce","keywords":["WooCommerce"],"description":"Accordion Panel","example":{},"__experimental":true,"parent":["woocommerce/accordion-item"],"supports":{"color":{"background":true,"gradient":true},"border":true,"interactivity":true,"spacing":{"padding":true,"margin":["top","bottom"],"blockGap":true,"__experimentalDefaultControls":{"padding":true,"blockGap":true}},"__experimentalBorder":{"color":true,"radius":true,"style":true,"width":true,"__experimentalDefaultControls":{"color":true,"radius":true,"style":true,"width":true}},"typography":{"fontSize":true,"lineHeight":true,"__experimentalFontFamily":true,"__experimentalFontWeight":true,"__experimentalFontStyle":true,"__experimentalTextTransform":true,"__experimentalTextDecoration":true,"__experimentalLetterSpacing":true,"__experimentalDefaultControls":{"fontSize":true}},"shadow":true,"layout":true},"attributes":{"allowedBlocks":{"type":"array"},"templateLock":{"type":["string","boolean"],"enum":["all","insert","contentOnly",false],"default":false},"openByDefault":{"type":"boolean","default":false},"isSelected":{"type":"boolean","default":false}},"textdomain":"woocommerce"}'),c=(0,s.jsx)(o.SVG,{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)(o.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M8.10417 6.00024H6.5C5.39543 6.00024 4.5 6.89567 4.5 8.00024V10.3336H6V8.00024C6 7.7241 6.22386 7.50024 6.5 7.50024H8.10417V6.00024ZM4.5 13.6669V16.0002C4.5 17.1048 5.39543 18.0002 6.5 18.0002H8.10417V16.5002H6.5C6.22386 16.5002 6 16.2764 6 16.0002V13.6669H4.5ZM10.3958 6.00024V7.50024H13.6042V6.00024H10.3958ZM15.8958 6.00024V7.50024H17.5C17.7761 7.50024 18 7.7241 18 8.00024V10.3336H19.5V8.00024C19.5 6.89567 18.6046 6.00024 17.5 6.00024H15.8958ZM19.5 13.6669H18V16.0002C18 16.2764 17.7761 16.5002 17.5 16.5002H15.8958V18.0002H17.5C18.6046 18.0002 19.5 17.1048 19.5 16.0002V13.6669ZM13.6042 18.0002V16.5002H10.3958V18.0002H13.6042Z",fill:"currentColor"})}),{name:p}=i,u={apiVersion:3,icon:c,example:{},edit:function({attributes:e}){const{allowedBlocks:t,templateLock:r,openByDefault:o,isSelected:a}=e,i=(0,l.__experimentalUseBorderProps)(e),c=(0,l.__experimentalUseColorProps)(e),p=(0,l.__experimentalGetSpacingClassesAndStyles)(e),u=(0,l.__experimentalGetShadowClassesAndStyles)(e),d=(0,l.useBlockProps)(),m=(0,l.useInnerBlocksProps)({className:"accordion-content__wrapper",style:{...p.style}},{allowedBlocks:t,template:[["core/paragraph",{}]],templateLock:r});return(0,s.jsx)("div",{...d,className:(0,n.A)(d.className,c.className,i.className,{"has-custom-font-size":d?.style?.fontSize}),style:{...i.style,...c.style,...u.style},"aria-hidden":!a&&!o,children:(0,s.jsx)("div",{...m})})},save:function({attributes:e}){const t=l.useBlockProps.save(),r=(0,l.__experimentalGetBorderClassesAndStyles)(e),o=(0,l.__experimentalGetColorClassesAndStyles)(e),a=(0,l.__experimentalGetSpacingClassesAndStyles)(e),i=(0,l.__experimentalGetShadowClassesAndStyles)(e);return(0,s.jsx)("div",{...t,className:(0,n.A)(t.className,o.className,r.className,{"has-custom-font-size":t?.style?.fontSize}),style:{...r.style,...o.style,...i.style},children:(0,s.jsx)("div",{className:"accordion-content__wrapper",style:{...a.style},children:(0,s.jsx)(l.InnerBlocks.Content,{})})})}};(0,a.registerBlockType)(i,u)},790:e=>{e.exports=window.ReactJSXRuntime}},a={};function l(e){var t=a[e];if(void 0!==t)return t.exports;var r=a[e]={exports:{}};return o[e].call(r.exports,r,r.exports,l),r.exports}l.m=o,e=[],l.O=(t,r,o,a)=>{if(!r){var n=1/0;for(p=0;p<e.length;p++){for(var[r,o,a]=e[p],s=!0,i=0;i<r.length;i++)(!1&a||n>=a)&&Object.keys(l.O).every((e=>l.O[e](r[i])))?r.splice(i--,1):(s=!1,a<n&&(n=a));if(s){e.splice(p--,1);var c=o();void 0!==c&&(t=c)}}return t}a=a||0;for(var p=e.length;p>0&&e[p-1][2]>a;p--)e[p]=e[p-1];e[p]=[r,o,a]},l.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return l.d(t,{a:t}),t},r=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,l.t=function(e,o){if(1&o&&(e=this(e)),8&o)return e;if("object"==typeof e&&e){if(4&o&&e.__esModule)return e;if(16&o&&"function"==typeof e.then)return e}var a=Object.create(null);l.r(a);var n={};t=t||[null,r({}),r([]),r(r)];for(var s=2&o&&e;"object"==typeof s&&!~t.indexOf(s);s=r(s))Object.getOwnPropertyNames(s).forEach((t=>n[t]=()=>e[t]));return n.default=()=>e,l.d(a,n),a},l.d=(e,t)=>{for(var r in t)l.o(t,r)&&!l.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},l.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),l.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},l.j=6766,(()=>{var e={6766:0};l.O.j=t=>0===e[t];var t=(t,r)=>{var o,a,[n,s,i]=r,c=0;if(n.some((t=>0!==e[t]))){for(o in s)l.o(s,o)&&(l.m[o]=s[o]);if(i)var p=i(l)}for(t&&t(r);c<n.length;c++)a=n[c],l.o(e,a)&&e[a]&&e[a][0](),e[a]=0;return l.O(p)},r=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var n=l.O(void 0,[94],(()=>l(424)));n=l.O(n),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["accordion-panel"]=n})();