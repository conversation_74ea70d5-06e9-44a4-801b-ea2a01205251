"use strict";Object.defineProperty(exports,"__esModule",{value:!0});const element_1=require("@wordpress/element"),data_1=require("@wordpress/data"),components_1=require("@woocommerce/components");async function getTaxonomiesMissingParents(e,t){const n=[],s={};return e.forEach((e=>{s[e.id]=e})),e.forEach((e=>{e.parent>0&&!s[e.parent]&&n.push(e.parent)})),n.length>0?(0,data_1.resolveSelect)("core").getEntityRecords("taxonomy",t,{include:n}).then((n=>getTaxonomiesMissingParents([...n,...e],t))):e}const PAGINATION_SIZE=30,useTaxonomySearch=(e,t={fetchParents:!0})=>{const[n,s]=(0,element_1.useState)(!1);return{searchEntity:async function(n){s(!0);let o=[];try{o=await(0,data_1.resolveSelect)("core").getEntityRecords("taxonomy",e,{per_page:30,search:(0,components_1.escapeHTML)(n)}),t?.fetchParents&&(o=await getTaxonomiesMissingParents(o,e))}finally{s(!1)}return o},isResolving:n}};exports.default=useTaxonomySearch;