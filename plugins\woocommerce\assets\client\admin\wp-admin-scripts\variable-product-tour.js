/*! For license information please see variable-product-tour.js.LICENSE.txt */
(()=>{"use strict";var e={94931:(e,t,r)=>{var o=r(51609),n=Symbol.for("react.element"),a=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),s=o.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};t.jsx=function(e,t,r){var o,i={},d=null,u=null;for(o in void 0!==r&&(d=""+r),void 0!==t.key&&(d=""+t.key),void 0!==t.ref&&(u=t.ref),t)a.call(t,o)&&!c.hasOwnProperty(o)&&(i[o]=t[o]);if(e&&e.defaultProps)for(o in t=e.defaultProps)void 0===i[o]&&(i[o]=t[o]);return{$$typeof:n,type:e,key:d,ref:u,props:i,_owner:s.current}}},39793:(e,t,r)=>{e.exports=r(94931)},51609:e=>{e.exports=window.React}},t={};const r=window.wp.element,o=window.wp.i18n,n=window.wc.components,a=window.wc.data,s=window.wc.tracks;var c=function r(o){var n=t[o];if(void 0!==n)return n.exports;var a=t[o]={exports:{}};return e[o](a,a.exports,r),a.exports}(39793);function i(e,t){return e[t]?.meta?.name}const d=()=>{const[e,t]=(0,r.useState)(!1),{updateUserPreferences:d,variable_product_tour_shown:u}=(0,a.useUserPreferences)(),p={steps:[{referenceElements:{desktop:".attribute_tab"},focusElement:{desktop:".attribute_tab"},meta:{name:"attributes",heading:(0,o.__)("Start by adding attributes","woocommerce"),descriptions:{desktop:(0,o.__)("Add attributes like size and color for customers to choose from on the product page. We will use them to generate product variations.","woocommerce")},primaryButton:{text:(0,o.__)("Got it","woocommerce")}}}],options:{effects:{spotlight:{interactivity:{enabled:!0,rootElementSelector:"#wpwrap"}},arrowIndicator:!0,liveResize:{mutation:!0,resize:!0,rootElementSelector:"#wpwrap"}}},closeHandler:(e,r)=>{d({variable_product_tour_shown:"yes"}),t(!1),r===e.length-1?(0,s.recordEvent)("variable_product_tour_completed",{step:i(e,r)}):(0,s.recordEvent)("variable_product_tour_dismissed",{step:i(e,r)})}};return(0,r.useEffect)((()=>{const e=document.querySelector("#product-type");if("yes"!==u&&e)return e.addEventListener("change",r),()=>{e.removeEventListener("change",r)};function r(){"variable"===e.value&&(t(!0),(0,s.recordEvent)("variable_product_tour_started",{step:i(p.steps,0)}))}})),e?(0,c.jsx)(n.TourKit,{config:p}):null},u=document.createElement("div");u.setAttribute("id","variable-product-tour-root"),(0,r.createRoot)(document.body.appendChild(u)).render((0,c.jsx)(d,{})),(window.wc=window.wc||{}).variableProductTour={}})();