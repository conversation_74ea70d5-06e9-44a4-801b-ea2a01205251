(()=>{"use strict";var e,t,o,l={1773:(e,t,o)=>{o.r(t),o.d(t,{metadata:()=>x,name:()=>f,settings:()=>w});const l=window.wp.components,r=window.wp.blocks;var n=o(4921),i=o(7723);const s=window.wp.blockEditor;var a=o(790);const c=({width:e,height:t})=>(0,a.jsx)(l.SVG,{width:e||24,height:t||24,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)(l.<PERSON>,{fillRule:"evenodd",clipRule:"evenodd",d:"M18.0041 10.5547L11.9996 16.0134L5.99512 10.5547L7.00413 9.44482L11.9996 13.9862L16.9951 9.44483L18.0041 10.5547Z",fill:"currentColor"})}),h=({width:e,height:t})=>(0,a.jsx)(l.SVG,{width:e||24,height:t||24,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)(l.Path,{d:"M11 12.5V17.5H12.5V12.5H17.5V11H12.5V6H11V11H6V12.5H11Z",fill:"currentColor"})}),u=({width:e,height:t})=>(0,a.jsxs)(l.SVG,{width:e||24,height:t||24,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)(l.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M20.75 12C20.75 16.8325 16.8325 20.75 12 20.75C7.16751 20.75 3.25 16.8325 3.25 12C3.25 7.16751 7.16751 3.25 12 3.25C16.8325 3.25 20.75 7.16751 20.75 12ZM12 19.25C16.0041 19.25 19.25 16.0041 19.25 12C19.25 7.99594 16.0041 4.75 12 4.75C7.99594 4.75 4.75 7.99594 4.75 12C4.75 16.0041 7.99594 19.25 12 19.25Z",fill:"currentColor"}),(0,a.jsx)(l.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M11.25 12.75V17H12.75V12.75H17V11.25H12.75V7H11.25V11.25H7V12.75H11.25Z",fill:"currentColor"})]}),d=({width:e,height:t})=>(0,a.jsx)(l.SVG,{width:e||24,height:t||24,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)(l.Path,{d:"M12 14.5L16.5 9.5L7.5 9.5L12 14.5Z",fill:"currentColor"})}),p=({width:e,height:t})=>(0,a.jsx)(l.SVG,{width:e||24,height:t||24,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)(l.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M9.82573 6.72441L15.2844 12.7289L9.82573 18.7334L8.71582 17.7244L13.2572 12.7289L8.71582 7.73342L9.82573 6.72441Z",fill:"currentColor"})}),g={plus:h,circlePlus:u,chevron:c,chevronRight:p,caret:d},m={plus:h,circlePlus:u,chevron:c,chevronRight:p,caret:d},x=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"woocommerce/accordion-header","title":"Accordion Header","category":"woocommerce","keywords":["WooCommerce"],"description":"Accordion header.","example":{},"__experimental":true,"parent":["woocommerce/accordion-item"],"supports":{"anchor":true,"color":{"background":true,"gradient":true},"align":false,"border":true,"interactivity":true,"spacing":{"padding":true,"margin":["top","bottom"],"__experimentalDefaultControls":{"padding":true,"margin":true}},"__experimentalBorder":{"color":true,"radius":true,"style":true,"width":true,"__experimentalDefaultControls":{"color":true,"radius":true,"style":true,"width":true}},"typography":{"textAlign":true,"fontSize":true,"__experimentalFontFamily":true,"__experimentalFontWeight":true,"__experimentalFontStyle":true,"__experimentalTextTransform":true,"__experimentalTextDecoration":true,"__experimentalLetterSpacing":true,"__experimentalDefaultControls":{"fontSize":true,"fontFamily":true}},"shadow":true,"layout":true},"attributes":{"openByDefault":{"type":"boolean","default":false},"title":{"type":"rich-text","source":"rich-text","selector":"span"},"level":{"type":"number","default":3},"levelOptions":{"type":"array"},"textAlignment":{"type":"string","default":"left"},"icon":{"type":["string","boolean"],"enum":["plus","chevron","chevronRight","caret","circlePlus",false],"default":"plus"},"iconPosition":{"type":"string","enum":["left","right"],"default":"right"}},"textdomain":"woocommerce"}'),_=(0,a.jsxs)(l.SVG,{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)(l.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M19.5 12.75L9.5 12.75L9.5 11.25L19.5 11.25L19.5 12.75Z",fill:"currentColor"}),(0,a.jsx)(l.Path,{d:"M4.5 9.5L8.5 12L4.5 14.5L4.5 9.5Z",fill:"currentColor"})]}),{name:f}=x,w={apiVersion:3,icon:_,example:{},edit:function({attributes:e,setAttributes:t}){const{level:o,title:r,textAlign:m,icon:x,iconPosition:_,levelOptions:f}=e,w="h"+o,v=(0,s.useBlockProps)(),y=(0,s.__experimentalUseBorderProps)(e),b=(0,s.__experimentalUseColorProps)(e),j=(0,s.__experimentalGetSpacingClassesAndStyles)(e),C=(0,s.__experimentalGetShadowClassesAndStyles)(e),O=g[x];return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(s.BlockControls,{children:(0,a.jsx)(l.ToolbarGroup,{children:(0,a.jsx)(s.HeadingLevelDropdown,{value:o,options:f,onChange:e=>t({level:e})})})}),(0,a.jsx)(s.InspectorControls,{children:(0,a.jsxs)(l.PanelBody,{title:(0,i.__)("Settings","woocommerce"),children:[(0,a.jsxs)(l.__experimentalToggleGroupControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,isBlock:!0,label:(0,i.__)("Icon","woocommerce"),value:x,onChange:e=>t({icon:e}),children:[(0,a.jsx)(l.__experimentalToggleGroupControlOptionIcon,{label:"Plus",icon:h,value:"plus"}),(0,a.jsx)(l.__experimentalToggleGroupControlOptionIcon,{label:"Chevron",icon:c,value:"chevron"}),(0,a.jsx)(l.__experimentalToggleGroupControlOptionIcon,{label:"Circle Plus",icon:u,value:"circlePlus"}),(0,a.jsx)(l.__experimentalToggleGroupControlOptionIcon,{label:"Caret",icon:d,value:"caret"}),(0,a.jsx)(l.__experimentalToggleGroupControlOptionIcon,{label:"Chevron Right",icon:p,value:"chevronRight"}),(0,a.jsx)(l.__experimentalToggleGroupControlOption,{label:"None",value:!1})]}),(0,a.jsxs)(l.__experimentalToggleGroupControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,isBlock:!0,label:(0,i.__)("Icon Position","woocommerce"),value:_,onChange:e=>{t({iconPosition:e})},children:[(0,a.jsx)(l.__experimentalToggleGroupControlOption,{label:"Left",value:"left"}),(0,a.jsx)(l.__experimentalToggleGroupControlOption,{label:"Right",value:"right"})]})]})},"setting"),(0,a.jsx)(w,{...v,className:(0,n.A)(v.className,b.className,y.className,"accordion-item__heading",{"has-custom-font-size":v.style.fontSize,"icon-position-left":"left"===_,[`has-text-align-${m}`]:m}),style:{...y.style,...b.style,...C.style},children:(0,a.jsxs)("button",{className:(0,n.A)("accordion-item__toggle"),style:{...j.style},children:[(0,a.jsx)(s.RichText,{allowedFormats:["core/bold","core/italic","core/image","core/strikethrough"],disableLineBreaks:!0,tagName:"span",value:r,onChange:e=>t({title:e}),placeholder:(0,i.__)("Accordion title","woocommerce")}),(0,a.jsx)("span",{className:(0,n.A)("accordion-item__toggle-icon",{[`has-icon-${x}`]:x}),style:{width:"1.2em",height:"1.2em"},children:O&&(0,a.jsx)(O,{width:"1.2em",height:"1.2em"})})]})})]})},save:function({attributes:e}){const{level:t,title:o,iconPosition:l,textAlign:r,icon:i}=e,c="h"+t,h=s.useBlockProps.save(),u=(0,s.__experimentalGetBorderClassesAndStyles)(e),d=(0,s.__experimentalGetColorClassesAndStyles)(e),p=(0,s.__experimentalGetSpacingClassesAndStyles)(e),g=(0,s.__experimentalGetShadowClassesAndStyles)(e),x=m[i];return(0,a.jsx)(c,{...h,className:(0,n.A)(h.className,d.className,u.className,"accordion-item__heading",{"has-custom-font-size":h?.style?.fontSize,"icon-position-left":"left"===l,[`has-text-align-${r}`]:r}),style:{...u.style,...d.style,...g.style},children:(0,a.jsxs)("button",{className:(0,n.A)("accordion-item__toggle"),style:{...p.style},children:[(0,a.jsx)(s.RichText.Content,{tagName:"span",value:o}),(0,a.jsx)("span",{className:(0,n.A)("accordion-item__toggle-icon",{[`has-icon-${i}`]:i}),style:{width:"1.2em",height:"1.2em"},children:i&&(0,a.jsx)(x,{width:"1.2em",height:"1.2em"})})]})})}};(0,r.registerBlockType)(x,w)},790:e=>{e.exports=window.ReactJSXRuntime},7723:e=>{e.exports=window.wp.i18n}},r={};function n(e){var t=r[e];if(void 0!==t)return t.exports;var o=r[e]={exports:{}};return l[e].call(o.exports,o,o.exports,n),o.exports}n.m=l,e=[],n.O=(t,o,l,r)=>{if(!o){var i=1/0;for(h=0;h<e.length;h++){for(var[o,l,r]=e[h],s=!0,a=0;a<o.length;a++)(!1&r||i>=r)&&Object.keys(n.O).every((e=>n.O[e](o[a])))?o.splice(a--,1):(s=!1,r<i&&(i=r));if(s){e.splice(h--,1);var c=l();void 0!==c&&(t=c)}}return t}r=r||0;for(var h=e.length;h>0&&e[h-1][2]>r;h--)e[h]=e[h-1];e[h]=[o,l,r]},n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},o=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,n.t=function(e,l){if(1&l&&(e=this(e)),8&l)return e;if("object"==typeof e&&e){if(4&l&&e.__esModule)return e;if(16&l&&"function"==typeof e.then)return e}var r=Object.create(null);n.r(r);var i={};t=t||[null,o({}),o([]),o(o)];for(var s=2&l&&e;"object"==typeof s&&!~t.indexOf(s);s=o(s))Object.getOwnPropertyNames(s).forEach((t=>i[t]=()=>e[t]));return i.default=()=>e,n.d(r,i),r},n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.j=6411,(()=>{var e={6411:0};n.O.j=t=>0===e[t];var t=(t,o)=>{var l,r,[i,s,a]=o,c=0;if(i.some((t=>0!==e[t]))){for(l in s)n.o(s,l)&&(n.m[l]=s[l]);if(a)var h=a(n)}for(t&&t(o);c<i.length;c++)r=i[c],n.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return n.O(h)},o=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))})();var i=n.O(void 0,[94],(()=>n(1773)));i=n.O(i),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["accordion-header"]=i})();