(()=>{"use strict";var t,e,o,r={6689:(t,e,o)=>{const r=window.wp.blocks;var n=o(4530),c=o(6012);const s=JSON.parse('{"name":"woocommerce/add-to-cart-with-options-quantity-selector","title":"Quantity Selector (Experimental)","description":"Display an input field with stepper to select the number of products to add to cart.","category":"woocommerce-product-elements","keywords":["WooCommerce"],"usesContext":["postId"],"ancestor":["woocommerce/add-to-cart-with-options"],"textdomain":"woocommerce","apiVersion":3,"supports":{"interactivity":true},"$schema":"https://schemas.wp.org/trunk/block.json","style":"file:../woocommerce/add-to-cart-with-options-quantity-selector-style.css"}'),i=window.wp.blockEditor,a=window.wp.components;var l=o(790);const p=()=>(0,l.jsxs)("div",{className:"quantity wc-block-components-quantity-selector",children:[(0,l.jsx)("button",{className:"wc-block-components-quantity-selector__button wc-block-components-quantity-selector__button--minus",children:"-"}),(0,l.jsx)("input",{type:"number",value:"1",className:"input-text qty text",readOnly:!0}),(0,l.jsx)("button",{className:"wc-block-components-quantity-selector__button wc-block-components-quantity-selector__button--plus",children:"+"})]}),u=window.wc.wcSettings,d=window.wc.wcTypes,w=(0,u.getSettingWithCoercion)("isBlockifiedAddToCart",!1,d.isBoolean),b=(0,u.getSetting)("isBlockTheme");w&&b&&(0,r.registerBlockType)(s,{edit:()=>{const t=(0,i.useBlockProps)({className:"wc-block-add-to-cart-with-options__quantity-selector"});return(0,l.jsx)("div",{...t,children:(0,l.jsx)(a.Disabled,{children:(0,l.jsx)(p,{})})})},attributes:s.attributes,icon:{src:(0,l.jsx)(n.A,{icon:c.A,className:"wc-block-editor-components-block-icon"})},save:()=>null})},1609:t=>{t.exports=window.React},790:t=>{t.exports=window.ReactJSXRuntime},6087:t=>{t.exports=window.wp.element},5573:t=>{t.exports=window.wp.primitives}},n={};function c(t){var e=n[t];if(void 0!==e)return e.exports;var o=n[t]={exports:{}};return r[t].call(o.exports,o,o.exports,c),o.exports}c.m=r,t=[],c.O=(e,o,r,n)=>{if(!o){var s=1/0;for(p=0;p<t.length;p++){for(var[o,r,n]=t[p],i=!0,a=0;a<o.length;a++)(!1&n||s>=n)&&Object.keys(c.O).every((t=>c.O[t](o[a])))?o.splice(a--,1):(i=!1,n<s&&(s=n));if(i){t.splice(p--,1);var l=r();void 0!==l&&(e=l)}}return e}n=n||0;for(var p=t.length;p>0&&t[p-1][2]>n;p--)t[p]=t[p-1];t[p]=[o,r,n]},c.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return c.d(e,{a:e}),e},o=Object.getPrototypeOf?t=>Object.getPrototypeOf(t):t=>t.__proto__,c.t=function(t,r){if(1&r&&(t=this(t)),8&r)return t;if("object"==typeof t&&t){if(4&r&&t.__esModule)return t;if(16&r&&"function"==typeof t.then)return t}var n=Object.create(null);c.r(n);var s={};e=e||[null,o({}),o([]),o(o)];for(var i=2&r&&t;"object"==typeof i&&!~e.indexOf(i);i=o(i))Object.getOwnPropertyNames(i).forEach((e=>s[e]=()=>t[e]));return s.default=()=>t,c.d(n,s),n},c.d=(t,e)=>{for(var o in e)c.o(e,o)&&!c.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},c.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),c.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},c.j=8851,(()=>{var t={8851:0};c.O.j=e=>0===t[e];var e=(e,o)=>{var r,n,[s,i,a]=o,l=0;if(s.some((e=>0!==t[e]))){for(r in i)c.o(i,r)&&(c.m[r]=i[r]);if(a)var p=a(c)}for(e&&e(o);l<s.length;l++)n=s[l],c.o(t,n)&&t[n]&&t[n][0](),t[n]=0;return c.O(p)},o=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];o.forEach(e.bind(null,0)),o.push=e.bind(null,o.push.bind(o))})();var s=c.O(void 0,[94],(()=>c(6689)));s=c.O(s),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["add-to-cart-with-options-quantity-selector"]=s})();