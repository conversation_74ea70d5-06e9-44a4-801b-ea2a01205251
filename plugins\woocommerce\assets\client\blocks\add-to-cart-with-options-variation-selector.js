(()=>{"use strict";var e,t,r,o={7209:(e,t,r)=>{const o=window.wp.blocks;var s=r(4530),c=r(6012);const i=JSON.parse('{"name":"woocommerce/add-to-cart-with-options-variation-selector","title":"Variation Selector (Experimental)","description":"Display a dropdown to select a variation to add to cart.","category":"woocommerce-product-elements","keywords":["WooCommerce"],"usesContext":["postId"],"ancestor":["woocommerce/add-to-cart-with-options"],"textdomain":"woocommerce","apiVersion":3,"supports":{"interactivity":true},"$schema":"https://schemas.wp.org/trunk/block.json"}'),n=window.wp.blockEditor,a=window.wc.wcBlocksSharedContext,p=window.wp.data,l="woocommerce/product-type-template-state",u="SWITCH_PRODUCT_TYPE",d="SET_PRODUCT_TYPES",w="REGISTER_LISTENER",m="UNREGISTER_LISTENER",y=window.wc.wcSettings,g=(0,y.getSetting)("productTypes",{}),f=Object.keys(g).map((e=>({slug:e,label:g[e]}))),b={productTypes:{list:f,current:f[0]?.slug},listeners:[]},v={switchProductType:e=>({type:u,current:e}),setProductTypes:e=>({type:d,productTypes:e}),registerListener:e=>({type:w,listener:e}),unregisterListener:e=>({type:m,listener:e})},T=(0,p.createReduxStore)(l,{reducer:(e=b,t)=>{switch(t.type){case d:return{...e,productTypes:{...e.productTypes,list:t.productTypes||[]}};case u:return{...e,productTypes:{...e.productTypes,current:t.current}};case w:return{...e,listeners:[...e.listeners,t.listener||""]};case m:return{...e,listeners:e.listeners.filter((e=>e!==t.listener))};default:return e}},actions:v,selectors:{getProductTypes:e=>e.productTypes.list,getCurrentProductType:e=>e.productTypes.list.find((t=>t.slug===e.productTypes.current)),getRegisteredListeners:e=>e.listeners}});(0,p.select)(l)||(0,p.register)(T);var h=r(7723);const _=[["woocommerce/add-to-cart-with-options-variation-selector-item",{},[["core/group",{layout:{type:"flex",orientation:"vertical",flexWrap:"nowrap"},style:{spacing:{blockGap:"0.5rem",margin:{top:"1rem",bottom:"1rem"}}}},[["woocommerce/add-to-cart-with-options-variation-selector-attribute-name",{fontSize:"medium"}],["woocommerce/add-to-cart-with-options-variation-selector-attribute-options"]]]]]];(0,h.__)("Color","woocommerce"),(0,h.__)("Blue","woocommerce"),(0,h.__)("Red","woocommerce"),(0,h.__)("Green","woocommerce"),(0,h.__)("Size","woocommerce"),(0,h.__)("Small","woocommerce"),(0,h.__)("Medium","woocommerce"),(0,h.__)("Large","woocommerce");var k=r(790);const O=window.wc.wcTypes,S=(0,y.getSettingWithCoercion)("isBlockifiedAddToCart",!1,O.isBoolean),P=(0,y.getSetting)("isBlockTheme");S&&P&&(0,o.registerBlockType)(i,{edit:function(e){const{className:t}=e.attributes,{current:r}=function(){const{productTypes:e,current:t,registeredListeners:r}=(0,p.useSelect)((e=>{const{getProductTypes:t,getCurrentProductType:r,getRegisteredListeners:o}=e(T);return{productTypes:t(),current:r(),registeredListeners:o()}}),[]),{switchProductType:o,registerListener:s,unregisterListener:c}=(0,p.useDispatch)(T);return{productTypes:e,current:t,set:o,registeredListeners:r,registerListener:s,unregisterListener:c}}(),{product:o}=(0,a.useProductDataContext)(),s=0===o.id?r?.slug:o.type,c=(0,n.useBlockProps)({className:t}),i=(0,n.useInnerBlocksProps)(c,{template:_,templateLock:"all"});return"variable"!==s?null:(0,k.jsx)("div",{...i})},attributes:i.attributes,icon:{src:(0,k.jsx)(s.A,{icon:c.A,className:"wc-block-editor-components-block-icon"})},save:function(){const e=n.useBlockProps.save(),t=n.useInnerBlocksProps.save({...e,role:"list"});return(0,k.jsx)("div",{...t})}})},1609:e=>{e.exports=window.React},790:e=>{e.exports=window.ReactJSXRuntime},6087:e=>{e.exports=window.wp.element},7723:e=>{e.exports=window.wp.i18n},5573:e=>{e.exports=window.wp.primitives}},s={};function c(e){var t=s[e];if(void 0!==t)return t.exports;var r=s[e]={exports:{}};return o[e].call(r.exports,r,r.exports,c),r.exports}c.m=o,e=[],c.O=(t,r,o,s)=>{if(!r){var i=1/0;for(l=0;l<e.length;l++){for(var[r,o,s]=e[l],n=!0,a=0;a<r.length;a++)(!1&s||i>=s)&&Object.keys(c.O).every((e=>c.O[e](r[a])))?r.splice(a--,1):(n=!1,s<i&&(i=s));if(n){e.splice(l--,1);var p=o();void 0!==p&&(t=p)}}return t}s=s||0;for(var l=e.length;l>0&&e[l-1][2]>s;l--)e[l]=e[l-1];e[l]=[r,o,s]},c.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return c.d(t,{a:t}),t},r=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,c.t=function(e,o){if(1&o&&(e=this(e)),8&o)return e;if("object"==typeof e&&e){if(4&o&&e.__esModule)return e;if(16&o&&"function"==typeof e.then)return e}var s=Object.create(null);c.r(s);var i={};t=t||[null,r({}),r([]),r(r)];for(var n=2&o&&e;"object"==typeof n&&!~t.indexOf(n);n=r(n))Object.getOwnPropertyNames(n).forEach((t=>i[t]=()=>e[t]));return i.default=()=>e,c.d(s,i),s},c.d=(e,t)=>{for(var r in t)c.o(t,r)&&!c.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},c.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),c.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},c.j=2475,(()=>{var e={2475:0};c.O.j=t=>0===e[t];var t=(t,r)=>{var o,s,[i,n,a]=r,p=0;if(i.some((t=>0!==e[t]))){for(o in n)c.o(n,o)&&(c.m[o]=n[o]);if(a)var l=a(c)}for(t&&t(r);p<i.length;p++)s=i[p],c.o(e,s)&&e[s]&&e[s][0](),e[s]=0;return c.O(l)},r=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var i=c.O(void 0,[94],(()=>c(7209)));i=c.O(i),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["add-to-cart-with-options-variation-selector"]=i})();