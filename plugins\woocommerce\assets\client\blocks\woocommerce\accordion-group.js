import*as e from"@wordpress/interactivity";var t={d:(e,o)=>{for(var n in o)t.o(o,n)&&!t.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:o[n]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const o=(s={getContext:()=>e.getContext,store:()=>e.store},i={},t.d(i,s),i),{state:n}=(0,o.store)("woocommerce/accordion",{state:{get isOpen(){const{isOpen:e,id:t}=(0,o.getContext)();return e.includes(t)}},actions:{toggle:()=>{const e=(0,o.getContext)(),{id:t,autoclose:s}=e;s?e.isOpen=n.isOpen?[]:[t]:n.isOpen?e.isOpen=e.isOpen.filter((e=>e!==t)):e.isOpen.push(t)}},callbacks:{initIsOpen:()=>{const e=(0,o.getContext)(),{id:t,openByDefault:n}=e;n&&e.isOpen.push(t)}}});var s,i;