(()=>{"use strict";var e,o,t,r={5182:(e,o,t)=>{const r=window.wp.blocks;var i=t(4530),n=t(6012);const s=JSON.parse('{"name":"woocommerce/add-to-cart-with-options-variation-selector-item","title":"Variation Selector Item Template (Experimental)","description":"A list item template that represents an attribute within the Variation Selector block.","category":"woocommerce-product-elements","keywords":["WooCommerce"],"usesContext":["postId"],"ancestor":["woocommerce/add-to-cart-with-options-variation-selector"],"textdomain":"woocommerce","apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json","supports":{"inserter":false,"interactivity":true}}');var c=t(6087);const a=window.wp.blockEditor,l=window.wp.data,d=window.wc.wcBlocksSharedContext;var m=t(7723);const p=[{id:1,taxonomy:"pa_color",name:(0,m.__)("Color","woocommerce"),has_variations:!0,terms:[{id:1,slug:"blue",name:(0,m.__)("Blue","woocommerce")},{id:2,slug:"red",name:(0,m.__)("Red","woocommerce")},{id:3,slug:"green",name:(0,m.__)("Green","woocommerce")}]},{id:2,taxonomy:"pa_size",name:(0,m.__)("Size","woocommerce"),has_variations:!0,terms:[{id:1,slug:"sm",name:(0,m.__)("Small","woocommerce")},{id:2,slug:"md",name:(0,m.__)("Medium","woocommerce")},{id:3,slug:"lg",name:(0,m.__)("Large","woocommerce")}]}];var u=t(790);function w({blocks:e,isSelected:o,onSelect:t}){const r=(0,a.__experimentalUseBlockPreview)({blocks:e}),i=(0,a.useInnerBlocksProps)({role:"listitem"},{templateLock:"insert"});return(0,u.jsxs)(u.Fragment,{children:[o?(0,u.jsx)("div",{...i}):(0,u.jsx)(u.Fragment,{}),(0,u.jsx)("div",{role:"listitem",style:{display:o?"none":void 0},children:(0,u.jsx)("div",{...r,role:"button",tabIndex:0,onClick:t,onKeyDown:t})})]})}const b=window.wc.wcSettings,v=window.wc.wcTypes,f=(0,b.getSettingWithCoercion)("isBlockifiedAddToCart",!1,v.isBoolean),h=(0,b.getSetting)("isBlockTheme");f&&h&&(0,r.registerBlockType)(s,{edit:function(e){const{clientId:o}=e,{className:t}=e.attributes,r=(0,a.useBlockProps)({className:t}),{product:i}=(0,d.useProductDataContext)(),n="variable"===i.type?i.attributes:p,{blocks:s}=(0,l.useSelect)((e=>{const{getBlocks:t}=e(a.store);return{blocks:t(o)}}),[o]),[m,b]=(0,c.useState)();return(0,u.jsx)("div",{...r,role:"list",children:n.map((e=>(0,u.jsx)(d.CustomDataProvider,{id:"attribute",data:e,children:(0,u.jsx)(w,{blocks:s,isSelected:(m||n[0]?.id)===e.id,onSelect:()=>b(e.id)})},e.id)))})},attributes:s.attributes,icon:{src:(0,u.jsx)(i.A,{icon:n.A,className:"wc-block-editor-components-block-icon"})},save:function(){const e=a.useBlockProps.save(),o=a.useInnerBlocksProps.save({...e,role:"listitem"});return(0,u.jsx)("div",{...o})}})},1609:e=>{e.exports=window.React},790:e=>{e.exports=window.ReactJSXRuntime},6087:e=>{e.exports=window.wp.element},7723:e=>{e.exports=window.wp.i18n},5573:e=>{e.exports=window.wp.primitives}},i={};function n(e){var o=i[e];if(void 0!==o)return o.exports;var t=i[e]={exports:{}};return r[e].call(t.exports,t,t.exports,n),t.exports}n.m=r,e=[],n.O=(o,t,r,i)=>{if(!t){var s=1/0;for(d=0;d<e.length;d++){for(var[t,r,i]=e[d],c=!0,a=0;a<t.length;a++)(!1&i||s>=i)&&Object.keys(n.O).every((e=>n.O[e](t[a])))?t.splice(a--,1):(c=!1,i<s&&(s=i));if(c){e.splice(d--,1);var l=r();void 0!==l&&(o=l)}}return o}i=i||0;for(var d=e.length;d>0&&e[d-1][2]>i;d--)e[d]=e[d-1];e[d]=[t,r,i]},n.n=e=>{var o=e&&e.__esModule?()=>e.default:()=>e;return n.d(o,{a:o}),o},t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,n.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var i=Object.create(null);n.r(i);var s={};o=o||[null,t({}),t([]),t(t)];for(var c=2&r&&e;"object"==typeof c&&!~o.indexOf(c);c=t(c))Object.getOwnPropertyNames(c).forEach((o=>s[o]=()=>e[o]));return s.default=()=>e,n.d(i,s),i},n.d=(e,o)=>{for(var t in o)n.o(o,t)&&!n.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:o[t]})},n.o=(e,o)=>Object.prototype.hasOwnProperty.call(e,o),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.j=8825,(()=>{var e={8825:0};n.O.j=o=>0===e[o];var o=(o,t)=>{var r,i,[s,c,a]=t,l=0;if(s.some((o=>0!==e[o]))){for(r in c)n.o(c,r)&&(n.m[r]=c[r]);if(a)var d=a(n)}for(o&&o(t);l<s.length;l++)i=s[l],n.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return n.O(d)},t=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];t.forEach(o.bind(null,0)),t.push=o.bind(null,t.push.bind(t))})();var s=n.O(void 0,[94],(()=>n(5182)));s=n.O(s),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["add-to-cart-with-options-variation-selector-item"]=s})();