/*! For license information please see index.js.LICENSE.txt */
(()=>{var e={93097:(e,n,t)=>{n.formatArgs=function(n){if(n[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+n[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const t="color: "+this.color;n.splice(1,0,t,"color: inherit");let r=0,o=0;n[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(r++,"%c"===e&&(o=r))})),n.splice(o,0,t)},n.save=function(e){try{e?n.storage.setItem("debug",e):n.storage.removeItem("debug")}catch(e){}},n.load=function(){let e;try{e=n.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},n.useColors=function(){return!("undefined"==typeof window||!window.process||"renderer"!==window.process.type&&!window.process.__nwjs)||("undefined"==typeof navigator||!navigator.userAgent||!navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},n.storage=function(){try{return localStorage}catch(e){}}(),n.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),n.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],n.log=console.debug||console.log||(()=>{}),e.exports=t(85824)(n);const{formatters:r}=e.exports;r.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},85824:(e,n,t)=>{e.exports=function(e){function n(e){let t,o,i,s=null;function a(...e){if(!a.enabled)return;const r=a,o=Number(new Date),i=o-(t||o);r.diff=i,r.prev=t,r.curr=o,t=o,e[0]=n.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let s=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((t,o)=>{if("%%"===t)return"%";s++;const i=n.formatters[o];if("function"==typeof i){const n=e[s];t=i.call(r,n),e.splice(s,1),s--}return t})),n.formatArgs.call(r,e),(r.log||n.log).apply(r,e)}return a.namespace=e,a.useColors=n.useColors(),a.color=n.selectColor(e),a.extend=r,a.destroy=n.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==s?s:(o!==n.namespaces&&(o=n.namespaces,i=n.enabled(e)),i),set:e=>{s=e}}),"function"==typeof n.init&&n.init(a),a}function r(e,t){const r=n(this.namespace+(void 0===t?":":t)+e);return r.log=this.log,r}function o(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return n.debug=n,n.default=n,n.coerce=function(e){return e instanceof Error?e.stack||e.message:e},n.disable=function(){const e=[...n.names.map(o),...n.skips.map(o).map((e=>"-"+e))].join(",");return n.enable(""),e},n.enable=function(e){let t;n.save(e),n.namespaces=e,n.names=[],n.skips=[];const r=("string"==typeof e?e:"").split(/[\s,]+/),o=r.length;for(t=0;t<o;t++)r[t]&&("-"===(e=r[t].replace(/\*/g,".*?"))[0]?n.skips.push(new RegExp("^"+e.slice(1)+"$")):n.names.push(new RegExp("^"+e+"$")))},n.enabled=function(e){if("*"===e[e.length-1])return!0;let t,r;for(t=0,r=n.skips.length;t<r;t++)if(n.skips[t].test(e))return!1;for(t=0,r=n.names.length;t<r;t++)if(n.names[t].test(e))return!0;return!1},n.humanize=t(59295),n.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((t=>{n[t]=e[t]})),n.names=[],n.skips=[],n.formatters={},n.selectColor=function(e){let t=0;for(let n=0;n<e.length;n++)t=(t<<5)-t+e.charCodeAt(n),t|=0;return n.colors[Math.abs(t)%n.colors.length]},n.enable(n.load()),n}},59295:e=>{var n=1e3,t=60*n,r=60*t,o=24*r,i=7*o;function s(e,n,t,r){var o=n>=1.5*t;return Math.round(e/t)+" "+r+(o?"s":"")}e.exports=function(e,a){a=a||{};var c,l,u=typeof e;if("string"===u&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var s=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(s){var a=parseFloat(s[1]);switch((s[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*a;case"weeks":case"week":case"w":return a*i;case"days":case"day":case"d":return a*o;case"hours":case"hour":case"hrs":case"hr":case"h":return a*r;case"minutes":case"minute":case"mins":case"min":case"m":return a*t;case"seconds":case"second":case"secs":case"sec":case"s":return a*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return a;default:return}}}}(e);if("number"===u&&isFinite(e))return a.long?(c=e,(l=Math.abs(c))>=o?s(c,l,o,"day"):l>=r?s(c,l,r,"hour"):l>=t?s(c,l,t,"minute"):l>=n?s(c,l,n,"second"):c+" ms"):function(e){var i=Math.abs(e);return i>=o?Math.round(e/o)+"d":i>=r?Math.round(e/r)+"h":i>=t?Math.round(e/t)+"m":i>=n?Math.round(e/n)+"s":e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},54399:(e,n,t)=>{var r,o,i;!function(t){if(t){var s={},a=t.TraceKit,c=[].slice,l="?",u=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/;s.noConflict=function(){return t.TraceKit=a,s},s.wrap=function(e){return function(){try{return e.apply(this,arguments)}catch(e){throw s.report(e),e}}},s.report=function(){var e,n,r,o,i=[],a=null,c=null;function l(e,n,t){var r=null;if(!n||s.collectWindowErrors){for(var o in i)if(f(i,o))try{i[o](e,n,t)}catch(e){r=e}if(r)throw r}}function d(n,t,r,o,i){if(c)s.computeStackTrace.augmentStackTraceWithInitialElement(c,t,r,n),m();else if(i)l(s.computeStackTrace(i),!0,i);else{var a,f={url:t,line:r,column:o},d=n;if("[object String]"==={}.toString.call(n)){var g=n.match(u);g&&(a=g[1],d=g[2])}f.func=s.computeStackTrace.guessFunctionName(f.url,f.line),f.context=s.computeStackTrace.gatherContext(f.url,f.line),l({name:a,message:d,mode:"onerror",stack:[f]},!0,null)}return!!e&&e.apply(this,arguments)}function g(e){l(s.computeStackTrace(e.reason),!0,e.reason)}function m(){var e=c,n=a;c=null,a=null,l(e,!1,n)}function p(e){if(c){if(a===e)return;m()}var n=s.computeStackTrace(e);throw c=n,a=e,setTimeout((function(){a===e&&m()}),n.incomplete?2e3:0),e}return p.subscribe=function(s){!0!==n&&(e=t.onerror,t.onerror=d,n=!0),!0!==o&&(r=t.onunhandledrejection,t.onunhandledrejection=g,o=!0),i.push(s)},p.unsubscribe=function(s){for(var a=i.length-1;a>=0;--a)i[a]===s&&i.splice(a,1);0===i.length&&(n&&(t.onerror=e,n=!1),o&&(t.onunhandledrejection=r,o=!1))},p}(),s.computeStackTrace=function(){var e={};function n(n){if("string"!=typeof n)return[];if(!f(e,n)){var r="",o="";try{o=t.document.domain}catch(e){}var i=/(.*)\:\/\/([^:\/]+)([:\d]*)\/{0,1}([\s\S]*)/.exec(n);i&&i[2]===o&&(r=function(e){if(!s.remoteFetching)return"";try{var n=function(){try{return new t.XMLHttpRequest}catch(e){return new t.ActiveXObject("Microsoft.XMLHTTP")}}();return n.open("GET",e,!1),n.send(""),n.responseText}catch(e){return""}}(n)),e[n]=r?r.split("\n"):[]}return e[n]}function r(e,t){var r,o=/function ([^(]*)\(([^)]*)\)/,i=/['"]?([0-9A-Za-z$_]+)['"]?\s*[:=]\s*(function|eval|new Function)/,s="",a=n(e);if(!a.length)return l;for(var c=0;c<10;++c)if(!d(s=a[t-c]+s)){if(r=i.exec(s))return r[1];if(r=o.exec(s))return r[1]}return l}function o(e,t){var r=n(e);if(!r.length)return null;var o=[],i=Math.floor(s.linesOfContext/2),a=i+s.linesOfContext%2,c=Math.max(0,t-i-1),l=Math.min(r.length,t+a-1);t-=1;for(var u=c;u<l;++u)d(r[u])||o.push(r[u]);return o.length>0?o:null}function i(e){return e.replace(/[\-\[\]{}()*+?.,\\\^$|#]/g,"\\$&")}function a(e){return i(e).replace("<","(?:<|&lt;)").replace(">","(?:>|&gt;)").replace("&","(?:&|&amp;)").replace('"','(?:"|&quot;)').replace(/\s+/g,"\\s+")}function c(e,t){for(var r,o,i=0,s=t.length;i<s;++i)if((r=n(t[i])).length&&(r=r.join("\n"),o=e.exec(r)))return{url:t[i],line:r.substring(0,o.index).split("\n").length,column:o.index-r.lastIndexOf("\n",o.index)-1};return null}function u(e,t,r){var o,s=n(t),a=new RegExp("\\b"+i(e)+"\\b");return r-=1,s&&s.length>r&&(o=a.exec(s[r]))?o.index:null}function g(e){if(!d(t&&t.document)){for(var n,r,o,s,l=[t.location.href],u=t.document.getElementsByTagName("script"),f=""+e,g=0;g<u.length;++g){var m=u[g];m.src&&l.push(m.src)}if(o=/^function(?:\s+([\w$]+))?\s*\(([\w\s,]*)\)\s*\{\s*(\S[\s\S]*\S)\s*\}\s*$/.exec(f)){var p=o[1]?"\\s+"+o[1]:"",h=o[2].split(",").join("\\s*,\\s*");n=i(o[3]).replace(/;$/,";?"),r=new RegExp("function"+p+"\\s*\\(\\s*"+h+"\\s*\\)\\s*{\\s*"+n+"\\s*}")}else r=new RegExp(i(f).replace(/\s+/g,"\\s+"));if(s=c(r,l))return s;if(o=/^function on([\w$]+)\s*\(event\)\s*\{\s*(\S[\s\S]*\S)\s*\}\s*$/.exec(f)){var w=o[1];if(n=a(o[2]),s=c(r=new RegExp("on"+w+"=[\\'\"]\\s*"+n+"\\s*[\\'\"]","i"),l[0]))return s;if(s=c(r=new RegExp(n),l))return s}return null}}function m(e){if(!e.stack)return null;for(var n,t,i,s=/^\s*at (.*?) ?\(((?:file|https?|blob|chrome-extension|native|eval|webpack|<anonymous>|\/).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,a=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)((?:file|https?|blob|chrome|webpack|resource|\[native).*?|[^@]*bundle)(?::(\d+))?(?::(\d+))?\s*$/i,c=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i,f=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,g=/\((\S*)(?::(\d+))(?::(\d+))\)/,m=e.stack.split("\n"),p=[],h=/^(.*) is undefined$/.exec(e.message),w=0,C=m.length;w<C;++w){if(t=s.exec(m[w])){var y=t[2]&&0===t[2].indexOf("native");t[2]&&0===t[2].indexOf("eval")&&(n=g.exec(t[2]))&&(t[2]=n[1],t[3]=n[2],t[4]=n[3]),i={url:y?null:t[2],func:t[1]||l,args:y?[t[2]]:[],line:t[3]?+t[3]:null,column:t[4]?+t[4]:null}}else if(t=c.exec(m[w]))i={url:t[2],func:t[1]||l,args:[],line:+t[3],column:t[4]?+t[4]:null};else{if(!(t=a.exec(m[w])))continue;t[3]&&t[3].indexOf(" > eval")>-1&&(n=f.exec(t[3]))?(t[3]=n[1],t[4]=n[2],t[5]=null):0!==w||t[5]||d(e.columnNumber)||(p[0].column=e.columnNumber+1),i={url:t[3],func:t[1]||l,args:t[2]?t[2].split(","):[],line:t[4]?+t[4]:null,column:t[5]?+t[5]:null}}!i.func&&i.line&&(i.func=r(i.url,i.line)),i.context=i.line?o(i.url,i.line):null,p.push(i)}return p.length?(p[0]&&p[0].line&&!p[0].column&&h&&(p[0].column=u(h[1],p[0].url,p[0].line)),{mode:"stack",name:e.name,message:e.message,stack:p}):null}function p(e,n,t,i){var s={url:n,line:t};if(s.url&&s.line){e.incomplete=!1,s.func||(s.func=r(s.url,s.line)),s.context||(s.context=o(s.url,s.line));var a=/ '([^']+)' /.exec(i);if(a&&(s.column=u(a[1],s.url,s.line)),e.stack.length>0&&e.stack[0].url===s.url){if(e.stack[0].line===s.line)return!1;if(!e.stack[0].line&&e.stack[0].func===s.func)return e.stack[0].line=s.line,e.stack[0].context=s.context,!1}return e.stack.unshift(s),e.partial=!0,!0}return e.incomplete=!0,!1}function h(e,n){for(var t,o,i,a=/function\s+([_$a-zA-Z\xA0-\uFFFF][_$a-zA-Z0-9\xA0-\uFFFF]*)?\s*\(/i,c=[],f={},d=!1,m=h.caller;m&&!d;m=m.caller)if(m!==w&&m!==s.report){if(o={url:null,func:l,args:[],line:null,column:null},m.name?o.func=m.name:(t=a.exec(m.toString()))&&(o.func=t[1]),void 0===o.func)try{o.func=t.input.substring(0,t.input.indexOf("{"))}catch(e){}if(i=g(m)){o.url=i.url,o.line=i.line,o.func===l&&(o.func=r(o.url,o.line));var C=/ '([^']+)' /.exec(e.message||e.description);C&&(o.column=u(C[1],i.url,i.line))}f[""+m]?d=!0:f[""+m]=!0,c.push(o)}n&&c.splice(0,n);var y={mode:"callers",name:e.name,message:e.message,stack:c};return p(y,e.sourceURL||e.fileName,e.line||e.lineNumber,e.message||e.description),y}function w(e,i){var s=null;i=null==i?0:+i;try{if(s=function(e){var n=e.stacktrace;if(n){for(var t,i=/ line (\d+).*script (?:in )?(\S+)(?:: in function (\S+))?$/i,s=/ line (\d+), column (\d+)\s*(?:in (?:<anonymous function: ([^>]+)>|([^\)]+))\((.*)\))? in (.*):\s*$/i,a=n.split("\n"),c=[],l=0;l<a.length;l+=2){var u=null;if((t=i.exec(a[l]))?u={url:t[2],line:+t[1],column:null,func:t[3],args:[]}:(t=s.exec(a[l]))&&(u={url:t[6],line:+t[1],column:+t[2],func:t[3]||t[4],args:t[5]?t[5].split(","):[]}),u){if(!u.func&&u.line&&(u.func=r(u.url,u.line)),u.line)try{u.context=o(u.url,u.line)}catch(e){}u.context||(u.context=[a[l+1]]),c.push(u)}}return c.length?{mode:"stacktrace",name:e.name,message:e.message,stack:c}:null}}(e),s)return s}catch(e){}try{if(s=m(e))return s}catch(e){}try{if(s=function(e){var i=e.message.split("\n");if(i.length<4)return null;var s,l=/^\s*Line (\d+) of linked script ((?:file|https?|blob)\S+)(?:: in function (\S+))?\s*$/i,u=/^\s*Line (\d+) of inline#(\d+) script in ((?:file|https?|blob)\S+)(?:: in function (\S+))?\s*$/i,d=/^\s*Line (\d+) of function script\s*$/i,g=[],m=t&&t.document&&t.document.getElementsByTagName("script"),p=[];for(var h in m)f(m,h)&&!m[h].src&&p.push(m[h]);for(var w=2;w<i.length;w+=2){var C=null;if(s=l.exec(i[w]))C={url:s[2],func:s[3],args:[],line:+s[1],column:null};else if(s=u.exec(i[w])){C={url:s[3],func:s[4],args:[],line:+s[1],column:null};var y=+s[1],F=p[s[2]-1];if(F){var v=n(C.url);if(v){var x=(v=v.join("\n")).indexOf(F.innerText);x>=0&&(C.line=y+v.substring(0,x).split("\n").length)}}}else if(s=d.exec(i[w])){var b=t.location.href.replace(/#.*$/,""),S=c(new RegExp(a(i[w+1])),[b]);C={url:b,func:"",args:[],line:S?S.line:s[1],column:null}}if(C){C.func||(C.func=r(C.url,C.line));var E=o(C.url,C.line),_=E?E[Math.floor(E.length/2)]:null;E&&_.replace(/^\s*/,"")===i[w+1].replace(/^\s*/,"")?C.context=E:C.context=[i[w+1]],g.push(C)}}return g.length?{mode:"multiline",name:e.name,message:i[0],stack:g}:null}(e),s)return s}catch(e){}try{if(s=h(e,i+1))return s}catch(e){}return{name:e.name,message:e.message,mode:"failed"}}return w.augmentStackTraceWithInitialElement=p,w.computeStackTraceFromStackProp=m,w.guessFunctionName=r,w.gatherContext=o,w.ofCaller=function(e){e=1+(null==e?0:+e);try{throw new Error}catch(n){return w(n,e+1)}},w.getSource=n,w}(),s.extendToAsynchronousCallbacks=function(){var e=function(e){var n=t[e];t[e]=function(){var e=c.call(arguments),t=e[0];return"function"==typeof t&&(e[0]=s.wrap(t)),n.apply?n.apply(this,e):n(e[0],e[1])}};e("setTimeout"),e("setInterval")},s.remoteFetching||(s.remoteFetching=!0),s.collectWindowErrors||(s.collectWindowErrors=!0),(!s.linesOfContext||s.linesOfContext<1)&&(s.linesOfContext=11),o=[],void 0===(i="function"==typeof(r=s)?r.apply(n,o):r)||(e.exports=i)}function f(e,n){return Object.prototype.hasOwnProperty.call(e,n)}function d(e){return void 0===e}}("undefined"!=typeof window?window:t.g)}},n={};function t(r){var o=n[r];if(void 0!==o)return o.exports;var i=n[r]={exports:{}};return e[r](i,i.exports,t),i.exports}t.n=e=>{var n=e&&e.__esModule?()=>e.default:()=>e;return t.d(n,{a:n}),n},t.d=(e,n)=>{for(var r in n)t.o(n,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:n[r]})},t.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),t.o=(e,n)=>Object.prototype.hasOwnProperty.call(e,n),t.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};(()=>{"use strict";t.r(r),t.d(r,{REMOTE_LOGGING_ERROR_DATA_FILTER:()=>m,REMOTE_LOGGING_JS_ERROR_ENDPOINT_FILTER:()=>h,REMOTE_LOGGING_LOG_ENDPOINT_FILTER:()=>p,REMOTE_LOGGING_SHOULD_SEND_ERROR_FILTER:()=>g,captureException:()=>_,init:()=>S,log:()=>E});var e=t(93097),n=t.n(e);const o=window.wc.wcSettings;var i=t(54399),s=t.n(i);const a=window.wp.hooks,c=window.wc.tracks;function l(e,n){const t={...e};for(const r in n)if(Object.prototype.hasOwnProperty.call(n,r)){const o=r;"extra"===o||"properties"===o?t[o]={...e[o],...n[o]}:"tags"===o&&Array.isArray(n[o])?t[o]=[...Array.isArray(e[o])?e[o]:[],...n[o]]:t[o]=n[o]}return t}const u=n()("wc:remote-logging"),f=e=>{console.warn("RemoteLogger: "+e)},d=(e,...n)=>{console.error("RemoteLogger: "+e,...n)},g="woocommerce_remote_logging_should_send_error",m="woocommerce_remote_logging_error_data",p="woocommerce_remote_logging_log_endpoint",h="woocommerce_remote_logging_js_error_endpoint",w=["path","page","step","task","tab","section","status","post_type","taxonomy","action"],C=e=>{const n=new URLSearchParams(e),t=(0,a.applyFilters)("woocommerce_remote_logging_request_uri_whitelist",w);for(const[e]of n)t.includes(e)||n.set(e,"xxxxxx");return n.toString()},y="wc_remote_logging_last_error_sent_time",F={message:"",feature:"woocommerce_core",host:window.location.hostname,tags:["woocommerce","js"],properties:{wp_version:(0,o.getSetting)("wpVersion"),wc_version:(0,o.getSetting)("wcVersion")}};class v{config;lastErrorSentTime=0;constructor(e){this.config=e,this.lastErrorSentTime=parseInt(localStorage.getItem(y)||"0",10)}async log(e,n,t){if(!n)return u("Empty message"),!1;const r=l(F,{message:n,severity:e,...t});return u("Logging:",r),await this.sendLog(r)}async error(e,n){if(this.isRateLimited())return;const t={...l(F,{message:e.message,severity:"error",...n,properties:{...n?.properties,request_uri:window.location.pathname+C(window.location.search)}}),trace:this.getFormattedStackFrame(s().computeStackTrace(e))};u("Logging error:",t),await this.sendError(t)}initializeErrorHandlers(){window.addEventListener("error",(e=>{u("Caught error event:",e),this.handleError(e.error).catch((e=>{u("Failed to handle error:",e)}))})),window.addEventListener("unhandledrejection",(async e=>{u("Caught unhandled rejection:",e);try{const n="string"==typeof e.reason?new Error(e.reason):e.reason;await this.handleError(n)}catch(e){u("Failed to handle unhandled rejection:",e)}}))}async sendLog(e){const n=new window.FormData;n.append("params",JSON.stringify(e));try{u("Sending log to API:",e);const t=(0,a.applyFilters)(p,"https://public-api.wordpress.com/rest/v1.1/logstash"),r=await window.fetch(t,{method:"POST",body:n});if(!r.ok)throw new Error(`response body: ${r.body}`);return!0}catch(e){return console.error("Failed to send log to API:",e),!1}}async handleError(e){const n=s().computeStackTrace(e);if(!this.shouldHandleError(e,n.stack))return void u("Irrelevant error. Skipping handling.",e);if((0,c.bumpStat)("error","unhandled-js-errors"),this.isRateLimited())return;const t={...l(F,{message:e.message,severity:"critical",tags:["js-unhandled-error"],properties:{request_uri:window.location.pathname+C(window.location.search)}}),trace:this.getFormattedStackFrame(n)},r=(0,a.applyFilters)(m,t);try{await this.sendError(r)}catch(e){console.error("Failed to send error:",e)}}async sendError(e){const n=new window.FormData;n.append("error",JSON.stringify(e));try{const t=(0,a.applyFilters)(h,"https://public-api.wordpress.com/rest/v1.1/js-error");u("Sending error to API:",e),await window.fetch(t,{method:"POST",body:n})}catch(e){console.error("Failed to send error to API:",e)}finally{this.lastErrorSentTime=Date.now(),localStorage.setItem(y,this.lastErrorSentTime.toString())}}getFormattedStackFrame(e){const n=e.stack.slice(0,10).map(this.getFormattedFrame).join("\n\n");return n.length>8192?n.substring(0,8192):n}getFormattedFrame(e,n){const t="?"!==e.func?e.func.replace(/"/g,""):"anonymous",r=e.url.replace(/"/g,""),o=e.context?e.context.map((e=>e.replace(/^"|"$/g,"").replace(/\\"/g,'"'))).filter((e=>""!==e.trim())).join("\n    ").substring(0,256):"";return`#${n+1} at ${t} (${r}:${e.line}:${e.column})`+(o?`\n${o}`:"")}shouldHandleError(e,n){const t=n.some((e=>e.url&&e.url.startsWith((0,o.getSetting)("wcAssetUrl"))));return(0,a.applyFilters)(g,t,e,n)}isRateLimited(){return Date.now()-this.lastErrorSentTime<this.config.errorRateLimitMs&&(u("Rate limit reached. Skipping send error"),!0)}}let x=null;function b(e){return window.wcSettings?.isRemoteLoggingEnabled?!!e||(f("RemoteLogger is not initialized. Call init() first."),!1):(u("Remote logging is disabled."),!1)}function S(e){if(window.wcSettings?.isRemoteLoggingEnabled)if(x)f("RemoteLogger is already initialized.");else try{x=new v(e),x.initializeErrorHandlers(),u("RemoteLogger initialized.")}catch(e){d("Failed to initialize RemoteLogger:",e)}else u("Remote logging is disabled.")}async function E(e,n,t){if(!b(x))return!1;try{return await x.log(e,n,t)}catch(e){return d("Failed to send log:",e),!1}}async function _(e,n){if(!b(x))return!1;try{await x.error(e,n)}catch(e){d("Failed to send log:",e)}}})(),(window.wc=window.wc||{}).remoteLogging=r})();