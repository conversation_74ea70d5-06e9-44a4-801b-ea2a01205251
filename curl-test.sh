#!/bin/bash

# cURL test script for SAP SoldTo API endpoint
# Usage: ./curl-test.sh [your-wordpress-site-url]

SITE_URL=${1:-"http://localhost"}
ENDPOINT="${SITE_URL}/wp-json/wc/v3/sap-soldto"

# Generate unique test data
TIMESTAMP=$(date +%s)
TEST_EMAIL="test${TIMESTAMP}@example.com"
CUSTOMER_ID="TEST_CUSTOMER_${TIMESTAMP}"

echo "Testing SAP SoldTo API Endpoint"
echo "================================"
echo "Endpoint: ${ENDPOINT}"
echo "Test Email: ${TEST_EMAIL}"
echo "Customer ID: ${CUSTOMER_ID}"
echo ""

# Test data
TEST_DATA='{
  "soldTo": {
    "customerId": "'${CUSTOMER_ID}'",
    "email": "'${TEST_EMAIL}'",
    "companyCode": "TEST_COMP",
    "countryCode": "US",
    "priceGroup": "STANDARD"
  },
  "billingAddress": {
    "company": "Test Company Inc.",
    "address": {
      "line1": "123 Test Street",
      "line2": "Suite 456",
      "city": "Test City",
      "postcode": "12345",
      "countryRegion": "US",
      "stateCounty": "CA"
    }
  },
  "shiptos": ["SHIPTO001", "SHIPTO002"]
}'

echo "Sending request..."
echo "=================="

# Make the request
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Basic $(echo -n 'your_username:your_password' | base64)" \
  -d "${TEST_DATA}" \
  "${ENDPOINT}" \
  -v

echo ""
echo ""
echo "Note: Replace 'your_username:your_password' with actual WordPress admin credentials"
echo "or use WooCommerce API keys if configured."
