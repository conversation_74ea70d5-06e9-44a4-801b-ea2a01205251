(()=>{var e,t,r,s={9848:(e,t,r)=>{"use strict";const s=window.wp.blocks;var i=r(5573),o=r(790);const l=(0,o.jsxs)(i.SVG,{xmlns:"http://www.w3.org/2000/SVG",viewBox:"0 0 24 24",children:[(0,o.jsx)("path",{fill:"none",d:"M0 0h24v24H0z"}),(0,o.jsx)("path",{d:"M17 6H7c-3.31 0-6 2.69-6 6s2.69 6 6 6h10c3.31 0 6-2.69 6-6s-2.69-6-6-6zm0 10H7c-2.21 0-4-1.79-4-4s1.79-4 4-4h10c2.21 0 4 1.79 4 4s-1.79 4-4 4zm0-7c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"})]});var c=r(4530),n=r(4921);const a=window.wp.blockEditor;var u=r(7723),d=r(9491);r(2796);const m=(0,d.withInstanceId)((({className:e,headingLevel:t,onChange:r,heading:s,instanceId:i})=>{const l=`h${t}`;return(0,o.jsxs)(l,{className:e,children:[(0,o.jsx)("label",{className:"screen-reader-text",htmlFor:`block-title-${i}`,children:(0,u.__)("Block title","woocommerce")}),(0,o.jsx)(a.PlainText,{id:`block-title-${i}`,className:"wc-block-editor-components-title",value:s,onChange:r,style:{backgroundColor:"transparent"}})]})})),p=window.wp.components,h=window.wc.wcBlocksData,w=window.wp.data;var f=r(6087),b=r(923),g=r.n(b);const y=(0,f.createContext)("page"),_=(y.Provider,(e,t,r)=>{const s=(0,f.useContext)(y);r=r||s;const i=(0,w.useSelect)((s=>s(h.QUERY_STATE_STORE_KEY).getValueForQueryKey(r,e,t)),[r,e]),{setQueryValue:o}=(0,w.useDispatch)(h.QUERY_STATE_STORE_KEY);return[i,(0,f.useCallback)((t=>{o(r,e,t)}),[r,e,o])]}),v=window.wc.wcSettings,k=window.wc.blocksComponents,x=window.wc.wcTypes,j=window.wp.url,S=(0,v.getSettingWithCoercion)("isRenderingPhpTemplate",!1,x.isBoolean);function C(e){if(S){const t=new URL(e);t.pathname=t.pathname.replace(/\/page\/[0-9]+/i,""),t.searchParams.delete("paged"),t.searchParams.forEach(((e,r)=>{r.match(/^query(?:-[0-9]+)?-page$/)&&t.searchParams.delete(r)})),window.location.href=t.href}else window.history.replaceState({},"",e)}r(9300);const N=({children:e})=>(0,o.jsx)("div",{className:"wc-block-filter-title-placeholder",children:e});r(4756);const O=(0,v.getSetting)("attributes",[]).reduce(((e,t)=>{const r=(s=t)&&s.attribute_name?{id:parseInt(s.attribute_id,10),name:s.attribute_name,taxonomy:"pa_"+s.attribute_name,label:s.attribute_label,orderby:s.attribute_orderby}:null;var s;return r&&r.id&&e.push(r),e}),[]),E=window.wc.priceFormat;var A=r(1924);const L=(e,t)=>Number.isFinite(e)&&Number.isFinite(t)?(0,u.sprintf)(/* translators: %1$s min price, %2$s max price */ /* translators: %1$s min price, %2$s max price */
(0,u.__)("Between %1$s and %2$s","woocommerce"),(0,E.formatPrice)(e),(0,E.formatPrice)(t)):Number.isFinite(e)?(0,u.sprintf)(/* translators: %s min price */ /* translators: %s min price */
(0,u.__)("From %s","woocommerce"),(0,E.formatPrice)(e)):(0,u.sprintf)(/* translators: %s max price */ /* translators: %s max price */
(0,u.__)("Up to %s","woocommerce"),(0,E.formatPrice)(t)),B=({type:e,name:t,prefix:r="",removeCallback:s=()=>null,showLabel:i=!0,displayStyle:l})=>{const n=r?(0,o.jsxs)(o.Fragment,{children:[r," ",t]}):t,a=(0,u.sprintf)(/* translators: 1: filter type, 2: attribute value used in the filter. For example: Remove Size Large filter. */ /* translators: 1: filter type, 2: attribute value used in the filter. For example: Remove Size Large filter. */
(0,u.__)("Remove %1$s %2$s filter","woocommerce"),e,t);return(0,o.jsxs)("li",{className:"wc-block-active-filters__list-item",children:[i&&(0,o.jsx)("span",{className:"wc-block-active-filters__list-item-type",children:e+": "}),"chips"===l?(0,o.jsx)(k.RemovableChip,{element:"span",text:n,onRemove:s,radius:"large",ariaLabel:a}):(0,o.jsxs)("span",{className:"wc-block-active-filters__list-item-name",children:[(0,o.jsxs)("button",{className:"wc-block-active-filters__list-item-remove",onClick:s,children:[(0,o.jsx)(c.A,{className:"wc-block-components-chip__remove-icon",icon:A.A,size:16}),(0,o.jsx)(k.Label,{screenReaderLabel:a})]}),n]})]},e+":"+t)},P=(...e)=>{if(!window)return;const t=window.location.href,r=(0,j.getQueryArgs)(t),s=(0,j.removeQueryArgs)(t,...Object.keys(r));e.forEach((e=>{if("string"==typeof e)return delete r[e];if("object"==typeof e){const t=Object.keys(e)[0],s=r[t].toString().split(",");r[t]=s.filter((r=>r!==e[t])).join(",")}}));const i=Object.fromEntries(Object.entries(r).filter((([,e])=>e)));C((0,j.addQueryArgs)(s,i))},T=["min_price","max_price","rating_filter","filter_","query_type_"],R=e=>{let t=!1;for(let r=0;T.length>r;r++){const s=T[r];if(s===e.substring(0,s.length)){t=!0;break}}return t};function F(e){const t=(0,f.useRef)(e);return g()(e,t.current)||(t.current=e),t.current}const Q=window.wp.htmlEntities;var $=r(9456);const M=({attributeObject:e,slugs:t=[],operator:r="in",displayStyle:s,isLoadingCallback:i})=>{const{results:l,isLoading:c}=(e=>{const{namespace:t,resourceName:r,resourceValues:s=[],query:i={},shouldSelect:o=!0}=e;if(!t||!r)throw new Error("The options object must have valid values for the namespace and the resource properties.");const l=(0,f.useRef)({results:[],isLoading:!0}),c=F(i),n=F(s),a=(()=>{const[,e]=(0,f.useState)();return(0,f.useCallback)((t=>{e((()=>{throw t}))}),[])})(),u=(0,w.useSelect)((e=>{if(!o)return null;const s=e(h.COLLECTIONS_STORE_KEY),i=[t,r,c,n],l=s.getCollectionError(...i);if(l){if(!(0,x.isError)(l))throw new Error("TypeError: `error` object is not an instance of Error constructor");a(l)}return{results:s.getCollection(...i),isLoading:!s.hasFinishedResolution("getCollection",i)}}),[t,r,n,c,o,a]);return null!==u&&(l.current=u),l.current})({namespace:"/wc/store/v1",resourceName:"products/attributes/terms",resourceValues:[e.id]}),[n,a]=_("attributes",[]);if((0,f.useEffect)((()=>{i(c)}),[c,i]),!Array.isArray(l)||!(0,x.isAttributeTermCollection)(l)||!(0,x.isAttributeQueryCollection)(n))return null;const d=e.label,m=(0,v.getSettingWithCoercion)("isRenderingPhpTemplate",!1,x.isBoolean);return(0,o.jsxs)("li",{children:[(0,o.jsxs)("span",{className:"wc-block-active-filters__list-item-type",children:[d,":"]}),(0,o.jsx)("ul",{children:t.map(((t,i)=>{const p=l.find((e=>e.slug===t));if(!p)return null;let h="";return i>0&&"and"===r&&(h=(0,o.jsx)("span",{className:"wc-block-active-filters__list-item-operator",children:(0,u.__)("All","woocommerce")})),B({type:d,name:(0,Q.decodeEntities)(p.name||t),prefix:h,isLoading:c,removeCallback:()=>{const r=n.find((({attribute:t})=>t===`pa_${e.name}`));1===r?.slug.length?P(`query_type_${e.name}`,`filter_${e.name}`):P({[`filter_${e.name}`]:t}),m||((e=[],t,r,s="")=>{const i=e.filter((e=>e.attribute===r.taxonomy)),o=i.length?i[0]:null;if(!(o&&o.slug&&Array.isArray(o.slug)&&o.slug.includes(s)))return;const l=o.slug.filter((e=>e!==s)),c=e.filter((e=>e.attribute!==r.taxonomy));l.length>0&&(o.slug=l.sort(),c.push(o)),t((0,$.di)(c).asc("attribute"))})(n,a,e,t)},showLabel:!1,displayStyle:s})}))})]})},I=({displayStyle:e,isLoading:t})=>t?(0,o.jsx)(o.Fragment,{children:[...Array("list"===e?2:3)].map(((t,r)=>(0,o.jsx)("li",{className:"list"===e?"show-loading-state-list":"show-loading-state-chips",children:(0,o.jsx)("span",{className:"show-loading-state__inner"})},r)))}):null,D=(0,f.createContext)({}),V=({attributes:e,isEditor:t=!1})=>{const r=(()=>{const{wrapper:e}=(0,f.useContext)(D);return t=>{e&&e.current&&(e.current.hidden=!t)}})(),s=function(){const e=(0,f.useRef)(!1);return(0,f.useEffect)((()=>(e.current=!0,()=>{e.current=!1})),[]),(0,f.useCallback)((()=>e.current),[])}()(),i=(0,v.getSettingWithCoercion)("isRenderingPhpTemplate",!1,x.isBoolean),[l,c]=(0,f.useState)(!0),a=(()=>{if(!window)return!1;const e=window.location.href,t=(0,j.getQueryArgs)(e),r=Object.keys(t);let s=!1;for(let e=0;r.length>e;e++){const t=r[e];if(R(t)){s=!0;break}}return s})()&&!t&&l,[d,m]=_("attributes",[]),[p,h]=_("stock_status",[]),[w,b]=_("min_price"),[g,y]=_("max_price"),[S,E]=_("rating"),A=(0,v.getSetting)("stockStatusOptions",[]),T=(0,v.getSetting)("attributes",[]),F=(0,f.useMemo)((()=>{if(a||0===p.length||!(0,x.isStockStatusQueryCollection)(p)||!(0,x.isStockStatusOptions)(A))return null;const t=(0,u.__)("Stock Status","woocommerce");return(0,o.jsxs)("li",{children:[(0,o.jsxs)("span",{className:"wc-block-active-filters__list-item-type",children:[t,":"]}),(0,o.jsx)("ul",{children:p.map((r=>B({type:t,name:A[r],removeCallback:()=>{if(P({filter_stock_status:r}),!i){const e=p.filter((e=>e!==r));h(e)}},showLabel:!1,displayStyle:e.displayStyle})))})]})}),[a,A,p,h,e.displayStyle,i]),Q=(0,f.useMemo)((()=>a||!Number.isFinite(w)&&!Number.isFinite(g)?null:B({type:(0,u.__)("Price","woocommerce"),name:L(w,g),removeCallback:()=>{P("max_price","min_price"),i||(b(void 0),y(void 0))},displayStyle:e.displayStyle})),[a,w,g,e.displayStyle,b,y,i]),$=(0,f.useMemo)((()=>!(0,x.isAttributeQueryCollection)(d)&&s||!d.length&&!(e=>{if(!window)return!1;const t=e.map((e=>`filter_${e.attribute_name}`)),r=window.location.href,s=(0,j.getQueryArgs)(r),i=Object.keys(s);let o=!1;for(let e=0;i.length>e;e++){const r=i[e];if(t.includes(r)){o=!0;break}}return o})(T)?(l&&c(!1),null):d.map((t=>{const r=(e=>{if(e)return O.find((t=>t.taxonomy===e))})(t.attribute);return r?(0,o.jsx)(M,{attributeObject:r,displayStyle:e.displayStyle,slugs:t.slug,operator:t.operator,isLoadingCallback:c},t.attribute):(l&&c(!1),null)}))),[d,s,T,l,e.displayStyle]);(0,f.useEffect)((()=>{if(!i)return;if(S.length&&S.length>0)return;const e=(window?(0,j.getQueryArg)(window.location.href,"rating_filter"):null)?.toString();e&&E(e.split(","))}),[i,S,E]);const V=(0,f.useMemo)((()=>{if(a||0===S.length||!(0,x.isRatingQueryCollection)(S))return null;const t=(0,u.__)("Rating","woocommerce");return(0,o.jsxs)("li",{children:[(0,o.jsxs)("span",{className:"wc-block-active-filters__list-item-type",children:[t,":"]}),(0,o.jsx)("ul",{children:S.map((r=>B({type:t,name:(0,u.sprintf)(/* translators: %s is referring to the average rating value */ /* translators: %s is referring to the average rating value */
(0,u.__)("Rated %s out of 5","woocommerce"),r),removeCallback:()=>{if(P({rating_filter:r}),!i){const e=S.filter((e=>e!==r));E(e)}},showLabel:!1,displayStyle:e.displayStyle})))})]})}),[a,S,E,e.displayStyle,i]);if(!a&&!(d.length>0||p.length>0||S.length>0||Number.isFinite(w)||Number.isFinite(g))&&!t)return r(!1),null;const z=`h${e.headingLevel}`,W=(0,o.jsx)(z,{className:"wc-block-active-filters__title",children:e.heading}),U=a?(0,o.jsx)(N,{children:W}):W;if(!(0,v.getSettingWithCoercion)("hasFilterableProducts",!1,x.isBoolean))return r(!1),null;r(!0);const q=(0,n.A)("wc-block-active-filters__list",{"wc-block-active-filters__list--chips":"chips"===e.displayStyle,"wc-block-active-filters--loading":a});return(0,o.jsxs)(o.Fragment,{children:[!t&&e.heading&&U,(0,o.jsxs)("div",{className:"wc-block-active-filters",children:[(0,o.jsx)("ul",{className:q,children:t?(0,o.jsxs)(o.Fragment,{children:[B({type:(0,u.__)("Size","woocommerce"),name:(0,u.__)("Small","woocommerce"),displayStyle:e.displayStyle}),B({type:(0,u.__)("Color","woocommerce"),name:(0,u.__)("Blue","woocommerce"),displayStyle:e.displayStyle})]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(I,{isLoading:a,displayStyle:e.displayStyle}),Q,F,$,V]})}),a?(0,o.jsx)("span",{className:"wc-block-active-filters__clear-all-placeholder"}):(0,o.jsx)("button",{className:"wc-block-active-filters__clear-all",onClick:()=>{(()=>{if(!window)return;const e=window.location.href,t=(0,j.getQueryArgs)(e),r=(0,j.removeQueryArgs)(e,...Object.keys(t)),s=Object.fromEntries(Object.keys(t).filter((e=>!R(e))).map((e=>[e,t[e]])));C((0,j.addQueryArgs)(r,s))})(),i||(b(void 0),y(void 0),m([]),h([]),E([]))},children:(0,o.jsx)(k.Label,{label:(0,u.__)("Clear All","woocommerce"),screenReaderLabel:(0,u.__)("Clear All Filters","woocommerce")})})]})]})};function z({children:e,className:t,actionLabel:r,onActionClick:s,...i}){return(0,o.jsx)(p.Notice,{...i,className:(0,n.$)("wc-block-editor-components-upgrade-downgrade-notice",t),actions:[{label:r,onClick:s,noDefaultClasses:!0,variant:"link"}],children:(0,o.jsx)("div",{className:"wc-block-editor-components-upgrade-downgrade-notice__text",children:e})})}r(8786),r(9969);const W=({clientId:e})=>{const{replaceBlock:t,removeBlock:r,updateBlockAttributes:i,selectBlock:l}=(0,w.useDispatch)("core/block-editor"),c=(0,f.createInterpolateElement)((0,u.__)("Upgrade all Filter blocks on this page for better performance and more customizability","woocommerce"),{strongText:(0,o.jsx)("strong",{children:(0,u.__)("Product Filters","woocommerce")})}),n=(0,u.__)("Upgrade all Filter blocks","woocommerce");return(0,o.jsx)(z,{isDismissible:!1,actionLabel:n,onActionClick:()=>{const{getBlocksByName:o,getBlockParentsByBlockName:c}=(0,w.select)("core/block-editor"),n=c(e,"woocommerce/filter-wrapper"),a=(0,s.createBlock)("woocommerce/product-filters");n.length?t(n[0],a):t(e,a),o("woocommerce/filter-wrapper").forEach((e=>{i(e,{lock:{remove:!1}}),r(e)})),["woocommerce/active-filters","woocommerce/price-filter","woocommerce/attribute-filter","woocommerce/stock-filter"].forEach((e=>{const t=o(e);t.length&&(i(t[0],{lock:{remove:!1}}),r(t[0]))})),l(a.clientId)},children:c})},U=(0,p.withSpokenMessages)((({attributes:e,setAttributes:t,clientId:r})=>{const{className:s,displayStyle:i,heading:l,headingLevel:c}=e,n=(0,a.useBlockProps)({className:s});return(0,o.jsxs)("div",{...n,children:[(0,o.jsxs)(a.InspectorControls,{children:[(0,o.jsx)(p.PanelBody,{children:(0,o.jsx)(W,{clientId:r})}),(0,o.jsx)(p.PanelBody,{title:(0,u.__)("Display Settings","woocommerce"),children:(0,o.jsxs)(p.__experimentalToggleGroupControl,{label:(0,u.__)("Display Style","woocommerce"),isBlock:!0,value:i,onChange:e=>t({displayStyle:e}),className:"wc-block-active-filter__style-toggle",children:[(0,o.jsx)(p.__experimentalToggleGroupControlOption,{value:"list",label:(0,u.__)("List","woocommerce")}),(0,o.jsx)(p.__experimentalToggleGroupControlOption,{value:"chips",label:(0,u.__)("Chips","woocommerce")})]})})]},"inspector"),l&&(0,o.jsx)(m,{className:"wc-block-active-filters__title",headingLevel:c,heading:l,onChange:e=>t({heading:e})}),(0,o.jsx)(p.Disabled,{children:(0,o.jsx)(V,{attributes:e,isEditor:!0})})]})})),q=JSON.parse('{"name":"woocommerce/active-filters","title":"Active Filters Controls","description":"Display the currently active filters.","category":"woocommerce","keywords":["WooCommerce"],"supports":{"interactivity":{"clientNavigation":false},"html":false,"multiple":false,"inserter":false,"color":{"text":true,"background":false},"lock":false},"attributes":{"displayStyle":{"type":"string","default":"list"},"headingLevel":{"type":"number","default":3}},"textdomain":"woocommerce","apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json"}'),G={heading:{type:"string",default:(0,u.__)("Active filters","woocommerce")}},Y=[{attributes:{...q.attributes,...G},save:({attributes:e})=>{const{className:t,displayStyle:r,heading:s,headingLevel:i}=e,l={"data-display-style":r,"data-heading":s,"data-heading-level":i};return(0,o.jsx)("div",{...a.useBlockProps.save({className:(0,n.A)("is-loading",t)}),...l,children:(0,o.jsx)("span",{"aria-hidden":!0,className:"wc-block-active-filters__placeholder"})})}}];(0,s.registerBlockType)(q,{apiVersion:3,icon:{src:(0,o.jsx)(c.A,{icon:l,className:"wc-block-editor-components-block-icon"})},attributes:{...q.attributes,...G},edit:U,save({attributes:e}){const{className:t}=e;return(0,o.jsx)("div",{...a.useBlockProps.save({className:(0,n.A)("is-loading",t)}),children:(0,o.jsx)("span",{"aria-hidden":!0,className:"wc-block-active-filters__placeholder"})})},deprecated:Y})},9300:()=>{},8786:()=>{},4756:()=>{},2796:()=>{},9969:()=>{},1609:e=>{"use strict";e.exports=window.React},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},9491:e=>{"use strict";e.exports=window.wp.compose},6087:e=>{"use strict";e.exports=window.wp.element},7723:e=>{"use strict";e.exports=window.wp.i18n},923:e=>{"use strict";e.exports=window.wp.isShallowEqual},5573:e=>{"use strict";e.exports=window.wp.primitives}},i={};function o(e){var t=i[e];if(void 0!==t)return t.exports;var r=i[e]={exports:{}};return s[e].call(r.exports,r,r.exports,o),r.exports}o.m=s,e=[],o.O=(t,r,s,i)=>{if(!r){var l=1/0;for(u=0;u<e.length;u++){for(var[r,s,i]=e[u],c=!0,n=0;n<r.length;n++)(!1&i||l>=i)&&Object.keys(o.O).every((e=>o.O[e](r[n])))?r.splice(n--,1):(c=!1,i<l&&(l=i));if(c){e.splice(u--,1);var a=s();void 0!==a&&(t=a)}}return t}i=i||0;for(var u=e.length;u>0&&e[u-1][2]>i;u--)e[u]=e[u-1];e[u]=[r,s,i]},o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},r=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,o.t=function(e,s){if(1&s&&(e=this(e)),8&s)return e;if("object"==typeof e&&e){if(4&s&&e.__esModule)return e;if(16&s&&"function"==typeof e.then)return e}var i=Object.create(null);o.r(i);var l={};t=t||[null,r({}),r([]),r(r)];for(var c=2&s&&e;"object"==typeof c&&!~t.indexOf(c);c=r(c))Object.getOwnPropertyNames(c).forEach((t=>l[t]=()=>e[t]));return l.default=()=>e,o.d(i,l),i},o.d=(e,t)=>{for(var r in t)o.o(t,r)&&!o.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.j=2507,(()=>{var e={2507:0};o.O.j=t=>0===e[t];var t=(t,r)=>{var s,i,[l,c,n]=r,a=0;if(l.some((t=>0!==e[t]))){for(s in c)o.o(c,s)&&(o.m[s]=c[s]);if(n)var u=n(o)}for(t&&t(r);a<l.length;a++)i=l[a],o.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return o.O(u)},r=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var l=o.O(void 0,[94],(()=>o(9848)));l=o.O(l),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["active-filters"]=l})();