(()=>{"use strict";var e,o,t,r={6036:(e,o,t)=>{t.r(o),t.d(o,{metadata:()=>d,name:()=>m,settings:()=>f});const r=window.wp.components,n=window.wp.blocks,c=window.wp.blockEditor;var l=t(7723),i=t(790);const a="woocommerce/accordion-item",s={name:a};var u=t(4921);const d=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"woocommerce/accordion-group","title":"Accordion Group","category":"woocommerce","keywords":["WooCommerce"],"description":"A group of headers and associated expandable content.","example":{},"__experimental":true,"supports":{"html":false,"align":["wide","full"],"background":{"backgroundImage":true,"backgroundSize":true,"__experimentalDefaultControls":{"backgroundImage":true}},"color":{"background":true,"gradient":true},"__experimentalBorder":{"color":true,"radius":true,"style":true,"width":true,"__experimentalDefaultControls":{"color":true,"radius":true,"style":true,"width":true}},"spacing":{"padding":true,"margin":["top","bottom"],"blockGap":true},"shadow":true,"layout":true,"interactivity":true},"attributes":{"iconPosition":{"type":"string","default":"right"},"autoclose":{"type":"boolean","default":false},"allowedBlocks":{"type":"array"}},"allowedBlocks":["woocommerce/accordion-item"],"textdomain":"woocommerce","viewScriptModule":"woocommerce/accordion-group","style":"file:../woocommerce/accordion-group-style.css"}'),p=(0,i.jsxs)(r.SVG,{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,i.jsx)(r.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M19.5 9.25L9.5 9.25L9.5 7.75L19.5 7.75L19.5 9.25Z",fill:"currentColor"}),(0,i.jsx)(r.Path,{d:"M4.5 6L8.5 8.5L4.5 11L4.5 6Z",fill:"currentColor"}),(0,i.jsx)(r.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M19.5 16.25L9.5 16.25L9.5 14.75L19.5 14.75L19.5 16.25Z",fill:"currentColor"}),(0,i.jsx)(r.Path,{d:"M4.5 13L8.5 15.5L4.5 18L4.5 13Z",fill:"currentColor"})]}),{name:m}=d,f={apiVersion:3,icon:p,example:{},edit:function({attributes:{autoclose:e},setAttributes:o}){const t=(0,c.useBlockProps)(),n=(0,c.useInnerBlocksProps)(t,{template:[[a],[a]],defaultBlock:s,directInsert:!0});return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(c.InspectorControls,{children:(0,i.jsx)(r.PanelBody,{title:(0,l.__)("Settings","woocommerce"),initialOpen:!0,children:(0,i.jsx)(r.ToggleControl,{isBlock:!0,__nextHasNoMarginBottom:!0,label:(0,l.__)("Auto-close","woocommerce"),onChange:e=>{o({autoclose:e})},checked:e,help:(0,l.__)("Automatically close accordions when a new one is opened.","woocommerce")})})},"setting"),(0,i.jsx)("div",{...n})]})},save:function({attributes:e}){const{iconPosition:o}=e,t=c.useBlockProps.save(),r=(0,u.A)({"icon-position-left":"left"===o},t.className);return(0,i.jsx)("div",{...c.useInnerBlocksProps.save({...t,className:r})})}};(0,n.registerBlockType)(d,f)},790:e=>{e.exports=window.ReactJSXRuntime},7723:e=>{e.exports=window.wp.i18n}},n={};function c(e){var o=n[e];if(void 0!==o)return o.exports;var t=n[e]={exports:{}};return r[e].call(t.exports,t,t.exports,c),t.exports}c.m=r,e=[],c.O=(o,t,r,n)=>{if(!t){var l=1/0;for(u=0;u<e.length;u++){for(var[t,r,n]=e[u],i=!0,a=0;a<t.length;a++)(!1&n||l>=n)&&Object.keys(c.O).every((e=>c.O[e](t[a])))?t.splice(a--,1):(i=!1,n<l&&(l=n));if(i){e.splice(u--,1);var s=r();void 0!==s&&(o=s)}}return o}n=n||0;for(var u=e.length;u>0&&e[u-1][2]>n;u--)e[u]=e[u-1];e[u]=[t,r,n]},c.n=e=>{var o=e&&e.__esModule?()=>e.default:()=>e;return c.d(o,{a:o}),o},t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,c.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var n=Object.create(null);c.r(n);var l={};o=o||[null,t({}),t([]),t(t)];for(var i=2&r&&e;"object"==typeof i&&!~o.indexOf(i);i=t(i))Object.getOwnPropertyNames(i).forEach((o=>l[o]=()=>e[o]));return l.default=()=>e,c.d(n,l),n},c.d=(e,o)=>{for(var t in o)c.o(o,t)&&!c.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:o[t]})},c.o=(e,o)=>Object.prototype.hasOwnProperty.call(e,o),c.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},c.j=6861,(()=>{var e={6861:0};c.O.j=o=>0===e[o];var o=(o,t)=>{var r,n,[l,i,a]=t,s=0;if(l.some((o=>0!==e[o]))){for(r in i)c.o(i,r)&&(c.m[r]=i[r]);if(a)var u=a(c)}for(o&&o(t);s<l.length;s++)n=l[s],c.o(e,n)&&e[n]&&e[n][0](),e[n]=0;return c.O(u)},t=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];t.forEach(o.bind(null,0)),t.push=o.bind(null,t.push.bind(t))})();var l=c.O(void 0,[94],(()=>c(6036)));l=c.O(l),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["accordion-group"]=l})();