(()=>{"use strict";var e={n:o=>{var t=o&&o.__esModule?()=>o.default:()=>o;return e.d(t,{a:t}),t},d:(o,t)=>{for(var n in t)e.o(t,n)&&!e.o(o,n)&&Object.defineProperty(o,n,{enumerable:!0,get:t[n]})},o:(e,o)=>Object.prototype.hasOwnProperty.call(e,o)};const o=window.wp.data,t=window.wp.i18n,n=window.wp.domReady;var c=e.n(n);const i=window.wc.wcSettings,s=window.wc.tracks,r=()=>document.querySelector(".editor-post-publish-button").classList.contains("is-busy")?Promise.resolve(!0):new Promise((e=>{window.requestAnimationFrame(e)})).then((()=>r())),a=()=>document.querySelector(".editor-post-publish-button").classList.contains("is-busy")?new Promise((e=>{window.requestAnimationFrame(e)})).then((()=>a())):Promise.resolve(!0);c()((()=>{const e=document.querySelector(".editor-post-publish-button");e&&e.addEventListener("click",r().then((()=>(()=>{const e=document.querySelector(".editor-post-publish-button");e.classList.contains("is-clicked")||(e.classList.add("is-clicked"),a().then((()=>{const e=null!==document.querySelector(".components-snackbar__content")?"snackbar":"default";(0,o.dispatch)("core/notices").removeNotice("SAVE_POST_NOTICE_ID"),(0,o.dispatch)("core/notices").createSuccessNotice((0,t.__)("🏠 Nice work creating your store’s homepage!","woocommerce"),{id:"WOOCOMMERCE_ONBOARDING_HOME_PAGE_NOTICE",type:e,actions:[{label:(0,t.__)("Continue setup.","woocommerce"),onClick:()=>{(0,s.queueRecordEvent)("tasklist_appearance_continue_setup",{}),window.location.href=(0,i.getAdminLink)("admin.php?page=wc-admin&task=appearance")}}]})})))})())))})),(window.wc=window.wc||{}).onboardingHomepageNotice={}})();