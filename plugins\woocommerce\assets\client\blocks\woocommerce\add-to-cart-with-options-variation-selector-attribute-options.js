import*as t from"@wordpress/interactivity";var e={d:(t,o)=>{for(var a in o)e.o(o,a)&&!e.o(t,a)&&Object.defineProperty(t,a,{enumerable:!0,get:o[a]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e)};const o=(a={getContext:()=>t.getContext,getElement:()=>t.getElement,store:()=>t.store},n={},e.d(n,a),n);var a,n;function r(t,e){if(!t)return null;const o=window.getComputedStyle(t)[e];if("rgba(0, 0, 0, 0)"!==o&&"transparent"!==o){const t=o.match(/\d+/g);if(!t||t.length<3)return null;const[e,a,n]=t.slice(0,3);return`rgb(${e}, ${a}, ${n})`}return r(t.parentElement,e)}!function(){const t=document.querySelector(".wc-block-add-to-cart-with-options-variation-selector-attribute-options__pills");if(!t)return;const e=document.createElement("style"),o=r(t,"backgroundColor")||"#fff",a=r(t,"color")||"#000";e.appendChild(document.createTextNode(`:where(.wc-block-add-to-cart-with-options-variation-selector-attribute-options__pill)[aria-checked="true"] {\n\t\t\t\tbackground-color: ${a};\n\t\t\t\tcolor: ${o};\n\t\t\t\tborder-color: ${a};\n\t\t\t}`)),document.head.appendChild(e)}();const{actions:i}=(0,o.store)("woocommerce/add-to-cart-with-options",{},{lock:"I acknowledge that using a private store means my plugin will inevitably break on the next store release."});function l(t,e){e?i.setAttribute(t,e):i.removeAttribute(t)}function s(){const t=(0,o.getContext)();l(t.name,t.selectedValue)}const c=({attributeName:t,attributeValue:e,selectedAttributes:o,availableVariations:a})=>{const n=o.some((e=>e.attribute===t))?o.length-1:o.length;return a.some((a=>(a.attributes["attribute_"+t.toLowerCase()]===e||""===a.attributes["attribute_"+t.toLowerCase()])&&o.filter((o=>{const n=a.attributes["attribute_"+o.attribute.toLowerCase()];return n===o.value||""===n&&(o.attribute.toLowerCase()!==t.toLowerCase()||e===o.value)})).length>=n))},{state:u,actions:d}=(0,o.store)("woocommerce/add-to-cart-with-options-variation-selector-attribute-options__pills",{state:{get isPillSelected(){const{selectedValue:t,option:e}=(0,o.getContext)();return t===e.value},get isPillDisabled(){const{name:t,option:e}=(0,o.getContext)(),{selectedAttributes:a,availableVariations:n}=(0,o.getContext)("woocommerce/add-to-cart-with-options");return!c({attributeName:t,attributeValue:e.value,selectedAttributes:a,availableVariations:n})},get pillTabIndex(){const{selectedValue:t,focused:e,option:a,options:n}=(0,o.getContext)();return u.isPillDisabled?-1:t||e||n[0]?.value!==a.value?u.isPillSelected||e===a.value?0:-1:0},get index(){const t=(0,o.getContext)();return t.options.findIndex((e=>e.value===t.option.value))}},actions:{toggleSelected(){if(u.isPillDisabled)return;const t=(0,o.getContext)();t.selectedValue===t.option.value?t.selectedValue="":t.selectedValue=t.option.value,t.focused=t.option.value,l(t.name,t.selectedValue)},handleKeyDown(t){let e=!1;switch(t.key){case" ":e=!0,d.toggleSelected();break;case"Up":case"ArrowUp":case"Left":case"ArrowLeft":{e=!0;const t=(0,o.getContext)(),{selectedAttributes:a,availableVariations:n}=(0,o.getContext)("woocommerce/add-to-cart-with-options"),{index:r}=u;if(r<=0)return;for(let e=r-1;e>=0;e--)if(c({attributeName:t.name,attributeValue:t.options[e].value,selectedAttributes:a,availableVariations:n}))return t.selectedValue=t.options[e].value,t.focused=t.selectedValue,void l(t.name,t.selectedValue);break}case"Down":case"ArrowDown":case"Right":case"ArrowRight":{e=!0;const t=(0,o.getContext)(),{selectedAttributes:a,availableVariations:n}=(0,o.getContext)("woocommerce/add-to-cart-with-options"),{index:r}=u;if(r>=t.options.length-1)return;for(let e=r+1;e<t.options.length;e++)if(c({attributeName:t.name,attributeValue:t.options[e].value,selectedAttributes:a,availableVariations:n}))return t.selectedValue=t.options[e].value,t.focused=t.selectedValue,void l(t.name,t.selectedValue);break}}e&&(t.stopPropagation(),t.preventDefault())}},callbacks:{setDefaultSelectedAttribute:s,watchSelected(){const{focused:t}=(0,o.getContext)();if(0===u.pillTabIndex&&t){const{ref:t}=(0,o.getElement)();t?.focus()}}}},{lock:!0});(0,o.store)("woocommerce/add-to-cart-with-options-variation-selector-attribute-options__dropdown",{state:{get isOptionDisabled(){const{name:t,option:e}=(0,o.getContext)();if(""===e.value)return!1;const{selectedAttributes:a,availableVariations:n}=(0,o.getContext)("woocommerce/add-to-cart-with-options");return!c({attributeName:t,attributeValue:e.value,selectedAttributes:a,availableVariations:n})}},actions:{handleChange(t){const e=(0,o.getContext)();e.selectedValue=t.currentTarget.value,l(e.name,e.selectedValue)}},callbacks:{setDefaultSelectedAttribute:s}},{lock:!0});