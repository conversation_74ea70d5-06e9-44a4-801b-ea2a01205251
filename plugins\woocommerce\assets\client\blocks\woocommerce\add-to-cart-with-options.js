import*as t from"@wordpress/interactivity";var e={64:t=>{t.exports=import("@woocommerce/stores/woocommerce/cart")}},r={};function n(t){var a=r[t];if(void 0!==a)return a.exports;var o=r[t]={exports:{}};return e[t](o,o.exports,n),o.exports}n.d=(t,e)=>{for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);const a=(l={getContext:()=>t.getContext,store:()=>t.store},p={},n.d(p,l),p),o="I acknowledge that using a private store means my plugin will inevitably break on the next store release.",{state:i}=(0,a.store)("woocommerce",{},{lock:o}),s=t=>{const e=(t=>{const e=t.target,r=e.parentElement?.querySelector(".input-text.qty.text");return r})(t);if(!e)return;const r=parseInt(e.value,10),n=parseInt(e.min,10),a=parseInt(e.max,10),o=parseInt(e.step,10);return{currentValue:isNaN(r)?0:r,minValue:isNaN(n)?1:n,maxValue:isNaN(a)?void 0:a,step:isNaN(o)?1:o,inputElement:e}},u=t=>{const e=new Event("change");t.dispatchEvent(e)},c=(0,a.store)("woocommerce/add-to-cart-with-options",{state:{get isFormValid(){const{productType:t,availableVariations:e,selectedAttributes:r}=(0,a.getContext)();if("variable"!==t)return!0;const n=((t,e)=>Array.isArray(t)&&Array.isArray(e)&&0!==t.length&&0!==e.length?t.find((t=>Object.entries(t.attributes).every((([t,r])=>e.some((e=>!("attribute_"+e.attribute.toLowerCase()!==t)&&(e.value===r||e.value&&""===r))))))):null)(e,r);return!!n}},actions:{setQuantity(t){(0,a.getContext)().quantity=t},setAttribute(t,e){const{selectedAttributes:r}=(0,a.getContext)(),n=r.findIndex((e=>e.attribute===t));n>=0?r[n]={attribute:t,value:e}:r.push({attribute:t,value:e})},removeAttribute(t){const{selectedAttributes:e}=(0,a.getContext)(),r=e.findIndex((e=>e.attribute===t));r>=0&&e.splice(r,1)},increaseQuantity:t=>{const e=s(t);if(!e)return;const{currentValue:r,maxValue:n,step:a,inputElement:o}=e,i=r+a;(void 0===n||i<=n)&&(c.actions.setQuantity(i),o.value=i.toString(),u(o))},decreaseQuantity:t=>{const e=s(t);if(!e)return;const{currentValue:r,minValue:n,step:a,inputElement:o}=e,i=r-a;i>=n&&(c.actions.setQuantity(i),o.value=i.toString(),u(o))},*handleSubmit(t){t.preventDefault(),yield Promise.resolve().then(n.bind(n,64));const{actions:e}=(0,a.store)("woocommerce",{},{lock:o}),{productId:r,quantity:s,selectedAttributes:u}=(0,a.getContext)(),c=i.cart?.items.find((t=>t.id===r)),l=c?.quantity||0;yield e.addCartItem({id:r,quantity:l+s,variation:u})}}},{lock:!0});var l,p;