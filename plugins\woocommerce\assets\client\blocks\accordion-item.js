(()=>{"use strict";var e,o,t,r={3302:(e,o,t)=>{t.r(o),t.d(o,{metadata:()=>u,name:()=>m,settings:()=>f});const r=window.wp.components,n=window.wp.blocks;var l=t(7723);const c=window.wp.blockEditor,a=window.wp.data;var s=t(6087),i=t(4921),d=t(790);const u=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"woocommerce/accordion-item","title":"Accordion","category":"woocommerce","keywords":["WooCommerce"],"description":"A single accordion that displays a header and expandable content.","example":{},"__experimental":true,"parent":["woocommerce/accordion-group"],"allowedBlocks":["woocommerce/accordion-header","woocommerce/accordion-panel"],"supports":{"align":["wide","full"],"color":{"background":true,"gradient":true},"interactivity":true,"spacing":{"margin":["top","bottom"],"blockGap":true},"__experimentalBorder":{"color":true,"radius":true,"style":true,"width":true,"__experimentalDefaultControls":{"color":true,"radius":true,"style":true,"width":true}},"shadow":true,"layout":true},"attributes":{"openByDefault":{"type":"boolean","default":false}},"textdomain":"woocommerce"}'),p=(0,d.jsxs)(r.SVG,{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,d.jsx)(r.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M19.5 9.5L9.5 9.5L9.5 8L19.5 8L19.5 9.5Z",fill:"currentColor"}),(0,d.jsx)(r.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M19.5 13L9.5 13L9.5 11.5L19.5 11.5L19.5 13Z",fill:"currentColor"}),(0,d.jsx)(r.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M19.5 16.3999L9.5 16.3999L9.5 14.8999L19.5 14.8999L19.5 16.3999Z",fill:"currentColor"}),(0,d.jsx)(r.Path,{d:"M4.5 6.25L8.5 8.75L4.5 11.25L4.5 6.25Z",fill:"currentColor"})]}),{name:m}=u,f={apiVersion:3,icon:p,example:{},edit:function({attributes:{openByDefault:e},clientId:o,setAttributes:t}){const n=(0,a.useSelect)((e=>{const{isBlockSelected:t,hasSelectedInnerBlock:r}=e(c.store);return t(o)||r(o,!0)}),[o]),u=(0,a.useSelect)((e=>e(c.store).getBlockOrder),[])(o)[1],{updateBlockAttributes:p,__unstableMarkNextChangeAsNotPersistent:m}=(0,a.useDispatch)(c.store);(0,s.useEffect)((()=>{u&&(m(),p(u,{isSelected:n}))}),[n,u,m,p]);const f=(0,c.useBlockProps)(),w=(0,c.useInnerBlocksProps)({...f,className:(0,i.A)(f.className,{"is-open":e||n})},{template:[["woocommerce/accordion-header",{}],["woocommerce/accordion-panel",{isSelected:!0,openByDefault:e}]],templateLock:"all",directInsert:!0});return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(c.InspectorControls,{children:(0,d.jsx)(r.PanelBody,{title:(0,l.__)("Settings","woocommerce"),children:(0,d.jsx)(r.ToggleControl,{label:(0,l.__)("Open by default","woocommerce"),__nextHasNoMarginBottom:!0,onChange:e=>{t({openByDefault:e}),u&&p(u,{openByDefault:e})},checked:e,help:(0,l.__)("Accordion content will be displayed by default.","woocommerce")})})},"setting"),(0,d.jsx)("div",{...w})]})},save:function({attributes:e}){const{openByDefault:o}=e,t=c.useBlockProps.save(),r=(0,i.A)({"is-open":o},t.className),n=c.useInnerBlocksProps.save({...t,className:r});return(0,d.jsx)("div",{...n})}};(0,n.registerBlockType)(u,f)},790:e=>{e.exports=window.ReactJSXRuntime},6087:e=>{e.exports=window.wp.element},7723:e=>{e.exports=window.wp.i18n}},n={};function l(e){var o=n[e];if(void 0!==o)return o.exports;var t=n[e]={exports:{}};return r[e].call(t.exports,t,t.exports,l),t.exports}l.m=r,e=[],l.O=(o,t,r,n)=>{if(!t){var c=1/0;for(d=0;d<e.length;d++){for(var[t,r,n]=e[d],a=!0,s=0;s<t.length;s++)(!1&n||c>=n)&&Object.keys(l.O).every((e=>l.O[e](t[s])))?t.splice(s--,1):(a=!1,n<c&&(c=n));if(a){e.splice(d--,1);var i=r();void 0!==i&&(o=i)}}return o}n=n||0;for(var d=e.length;d>0&&e[d-1][2]>n;d--)e[d]=e[d-1];e[d]=[t,r,n]},l.n=e=>{var o=e&&e.__esModule?()=>e.default:()=>e;return l.d(o,{a:o}),o},t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,l.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var n=Object.create(null);l.r(n);var c={};o=o||[null,t({}),t([]),t(t)];for(var a=2&r&&e;"object"==typeof a&&!~o.indexOf(a);a=t(a))Object.getOwnPropertyNames(a).forEach((o=>c[o]=()=>e[o]));return c.default=()=>e,l.d(n,c),n},l.d=(e,o)=>{for(var t in o)l.o(o,t)&&!l.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:o[t]})},l.o=(e,o)=>Object.prototype.hasOwnProperty.call(e,o),l.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},l.j=1289,(()=>{var e={1289:0};l.O.j=o=>0===e[o];var o=(o,t)=>{var r,n,[c,a,s]=t,i=0;if(c.some((o=>0!==e[o]))){for(r in a)l.o(a,r)&&(l.m[r]=a[r]);if(s)var d=s(l)}for(o&&o(t);i<c.length;i++)n=c[i],l.o(e,n)&&e[n]&&e[n][0](),e[n]=0;return l.O(d)},t=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];t.forEach(o.bind(null,0)),t.push=o.bind(null,t.push.bind(t))})();var c=l.O(void 0,[94],(()=>l(3302)));c=l.O(c),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["accordion-item"]=c})();