"use strict";var __importDefault=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(exports,"__esModule",{value:!0}),exports.TextAreaBlockEdit=TextAreaBlockEdit;const i18n_1=require("@wordpress/i18n"),block_templates_1=require("@woocommerce/block-templates"),element_1=require("@wordpress/element"),components_1=require("@wordpress/components"),compose_1=require("@wordpress/compose"),block_editor_1=require("@wordpress/block-editor"),classnames_1=__importDefault(require("classnames")),toolbar_button_rtl_1=require("./toolbar/toolbar-button-rtl"),toolbar_button_alignment_1=__importDefault(require("./toolbar/toolbar-button-alignment")),use_clear_selected_block_on_blur_1=require("../../../hooks/use-clear-selected-block-on-blur"),use_product_entity_prop_1=__importDefault(require("../../../hooks/use-product-entity-prop")),label_1=require("../../../components/label/label");function TextAreaBlockEdit({attributes:e,setAttributes:t,context:{postType:o}}){const{property:r,label:l,placeholder:n,help:a,required:c,note:i,tooltip:_,disabled:s=!1,align:u,allowedFormats:d,direction:m,mode:p="rich-text"}=e,b=(0,block_templates_1.useWooBlockProps)(e,{className:"wp-block-woocommerce-product-text-area-field",style:{direction:m}}),f=(0,compose_1.useInstanceId)(TextAreaBlockEdit,"wp-block-woocommerce-product-content-field__content"),k=f.toString()+"__label";if(!r)throw new Error((0,i18n_1.__)("Property attribute is required.","woocommerce"));const[h,q]=(0,use_product_entity_prop_1.default)(r,{postType:o}),{handleBlur:g}=(0,use_clear_selected_block_on_blur_1.useClearSelectedBlockOnBlur)(),w=(0,element_1.useRef)(null),x=(0,element_1.useRef)(null),y="rich-text"===p,B="plain-text"===p;return(0,element_1.createElement)("div",{...b},y&&(0,element_1.createElement)(block_editor_1.BlockControls,{group:"block"},(0,element_1.createElement)(toolbar_button_alignment_1.default,{align:u,setAlignment:function(e){t({align:e})}}),(0,element_1.createElement)(toolbar_button_rtl_1.RTLToolbarButton,{direction:m,onChange:function(e){t({direction:e})}})),(0,element_1.createElement)(components_1.BaseControl,{id:f.toString(),label:(0,element_1.createElement)(label_1.Label,{label:l||"",labelId:k,required:c,note:i,tooltip:_,onClick:y?function(){w.current?.focus()}:function(){x.current?.focus()}}),help:a},y&&(0,element_1.createElement)(block_editor_1.RichText,{ref:w,id:f.toString(),"aria-labelledby":k,identifier:"content",tagName:"p",value:h||"",onChange:q,"data-empty":Boolean(h),className:(0,classnames_1.default)("components-summary-control",{[`has-text-align-${u}`]:u}),dir:m,allowedFormats:d,placeholder:n,required:c,"aria-required":c,readOnly:s,onBlur:g}),B&&(0,element_1.createElement)(components_1.TextareaControl,{ref:x,"aria-labelledby":k,value:h||"",onChange:q,placeholder:n,required:c,disabled:s,onBlur:g})))}