import*as e from"@wordpress/interactivity";var t={d:(e,o)=>{for(var r in o)t.o(o,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:o[r]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const o=(s={getContext:()=>e.getContext,store:()=>e.store},n={},t.d(n,s),n),r={actions:{startZoom:e=>{var t;const r=e.target;if(!r.classList.contains("wc-block-woocommerce-product-gallery-large-image__image"))return a.resetZoom(e);const s=e.target,n=e.offsetX/s.clientWidth*100,i=e.offsetY/s.clientHeight*100,{selectedImageId:l}=(0,o.getContext)(undefined);l===parseInt(null!==(t=r.getAttribute("data-image-id"))&&void 0!==t?t:"0",10)&&(r.style.transform="scale(1.3)",r.style.transformOrigin=`${n}% ${i}%`)},resetZoom:e=>{const t=e.target;t&&(t.style.transform="scale(1.0)",t.style.transformOrigin="")}}},{actions:a}=(0,o.store)("woocommerce/product-gallery",r,{lock:"I acknowledge that using a private store means my plugin will inevitably break on the next store release."});var s,n;