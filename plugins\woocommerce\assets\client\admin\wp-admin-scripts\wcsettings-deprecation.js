(()=>{"use strict";window.wp.element;const e=window.wp.i18n,t=window.wc.wcSettings,n=["wcAdminSettings","preloadSettings"],o=(0,t.getSetting)("admin",{}),i=Object.keys(o).reduce(((e,t)=>(n.includes(t)||(e[t]=o[t]),e)),{}),c={onboarding:{profile:"Deprecated: wcSettings.admin.onboarding.profile is deprecated. It is planned to be released in WooCommerce 10.0.0. Please use `getProfileItems` from the onboarding store. See https://github.com/woocommerce/woocommerce/tree/trunk/packages/js/data/src/onboarding for more information.",euCountries:"Deprecated: wcSettings.admin.onboarding.euCountries is deprecated. Please use `/wc/v3/data/continents/eu` from the REST API. See https://woocommerce.github.io/woocommerce-rest-api-docs/#list-all-continents for more information.",localInfo:'Deprecated: wcSettings.admin.onboarding.localInfo is deprecated. Please use `include WC()->plugin_path() . "/i18n/locale-info.php"` instead.',currencySymbols:'"Deprecated: wcSettings.admin.onboarding.currencySymbols is deprecated. Please use get_woocommerce_currency_symbols() function instead.'}};(0,t.getSetting)("adminUrl"),(0,t.getSetting)("countries"),(0,t.getSetting)("currency"),(0,t.getSetting)("locale"),(0,t.getSetting)("siteTitle"),(0,t.getSetting)("wcAssetUrl"),function(t,o=!1,c=e=>e){if(n.includes(t))throw new Error((0,e.__)("Mutable settings should be accessed via data store.","woocommerce"));c(i.hasOwnProperty(t)?i[t]:o,o)}("orderStatuses"),window.wcSettings&&c&&Object.keys(c).length,(window.wc=window.wc||{}).wcsettingsDeprecation={}})();