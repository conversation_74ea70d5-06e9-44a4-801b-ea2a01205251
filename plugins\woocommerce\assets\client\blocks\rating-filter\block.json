{"name": "woocommerce/rating-filter", "title": "Filter by Rating Controls", "description": "Enable customers to filter the product grid by rating.", "category": "woocommerce", "keywords": ["WooCommerce"], "supports": {"interactivity": {"clientNavigation": false}, "html": false, "multiple": false, "color": {"background": true, "text": true, "button": true}, "inserter": false, "lock": false}, "attributes": {"className": {"type": "string", "default": ""}, "showCounts": {"type": "boolean", "default": false}, "displayStyle": {"type": "string", "default": "list"}, "showFilterButton": {"type": "boolean", "default": false}, "selectType": {"type": "string", "default": "multiple"}, "isPreview": {"type": "boolean", "default": false}}, "textdomain": "woocommerce", "apiVersion": 3, "$schema": "https://schemas.wp.org/trunk/block.json"}