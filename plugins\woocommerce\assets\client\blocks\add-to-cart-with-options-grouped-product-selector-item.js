(()=>{"use strict";var e,t,o,r={123:(e,t,o)=>{const r=window.wp.blocks;var s=o(4530),c=o(6012);const i=JSON.parse('{"name":"woocommerce/add-to-cart-with-options-grouped-product-selector-item","title":"Grouped Product Selector Item Template (Experimental)","description":"A list item template that represents a child product within the Grouped Product Selector block.","category":"woocommerce-product-elements","keywords":["WooCommerce"],"usesContext":["postId"],"ancestor":["woocommerce/add-to-cart-with-options-grouped-product-selector"],"textdomain":"woocommerce","apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json","supports":{"inserter":false,"interactivity":true},"style":"file:../woocommerce/add-to-cart-with-options-grouped-product-selector-item-style.css"}');var n=o(6087);const d=window.wp.blockEditor;var a=o(9491);window.wp.url;const l=window.wp.apiFetch;var p=o.n(l);const u=window.wc.wcSettings;var w=o(790);const h=(0,a.createHigherOrderComponent)((e=>class extends n.Component{state={error:null,loading:!1,product:"preview"===this.props.attributes.productId?this.props.attributes.previewProduct:null};componentDidMount(){this.loadProduct()}componentDidUpdate(e){e.attributes.productId!==this.props.attributes.productId&&this.loadProduct()}loadProduct=()=>{const{productId:e}=this.props.attributes;"preview"!==e&&(e?(this.setState({loading:!0}),(e=>p()({path:`/wc/store/v1/products/${e}`}))(e).then((e=>{this.setState({product:e,loading:!1,error:null})})).catch((async e=>{const t=await(async e=>{if(!("json"in e))return{code:e.code||"",message:e.message,type:e.type||"general"};try{const t=await e.json();return{code:t.code||"",message:t.message,type:t.type||"api"}}catch(e){return{message:e.message,type:"general"}}})(e);this.setState({product:null,loading:!1,error:t})}))):this.setState({product:null,loading:!1,error:null}))};render(){const{error:t,loading:o,product:r}=this.state;return(0,w.jsx)(e,{...this.props,error:t,getProduct:this.loadProduct,isLoading:o,product:r})}}),"withProduct"),m=window.wc.wcBlocksSharedContext,g=window.wp.data,v=window.wc.data,b=h((function({attributes:e,isLoading:t,product:o,blocks:r,isSelected:s,onSelect:c}){const i=(0,d.__experimentalUseBlockPreview)({blocks:r}),n=(0,d.useInnerBlocksProps)({role:"listitem"},{templateLock:"insert"});return(0,w.jsx)(d.BlockContextProvider,{value:{postId:e.productId,postType:"product"},children:(0,w.jsxs)(m.ProductDataContextProvider,{product:o,isLoading:t,children:[s?(0,w.jsx)("div",{...n}):(0,w.jsx)(w.Fragment,{}),(0,w.jsx)("div",{role:"listitem",style:{display:s?"none":void 0},children:(0,w.jsx)("div",{...i,role:"button",tabIndex:0,onClick:c,onKeyDown:c})})]})})})),f=window.wc.wcTypes,y=(0,u.getSettingWithCoercion)("isBlockifiedAddToCart",!1,f.isBoolean),k=(0,u.getSetting)("isBlockTheme");y&&k&&(0,r.registerBlockType)(i,{edit:function(e){const{clientId:t}=e,{className:o}=e.attributes,r=(0,d.useBlockProps)({className:o}),{product:s}=(0,m.useProductDataContext)(),[c,i]=(0,n.useState)(null);(0,n.useEffect)((()=>{const e=async e=>{e&&0!==e.length&&(0,g.resolveSelect)(v.productsStore).getProducts({include:e,per_page:e.length,_fields:["id"]}).then((e=>{i(e)}))};c||(0!==s.id&&"grouped"===s.type?e(s.grouped_products):0===s.id&&(0,g.resolveSelect)(v.productsStore).getProducts({type:"grouped",per_page:1}).then((t=>{t.length>0?e(t[0].grouped_products):(0,g.resolveSelect)(v.productsStore).getProducts({per_page:3,_fields:["id"]}).then((e=>{e.length>0&&i(e)}))})))}),[c,s]);const{blocks:a}=(0,g.useSelect)((e=>{const{getBlocks:o}=e(d.store);return{blocks:o(t)}}),[t]),[l,p]=(0,n.useState)();return(0,w.jsx)("div",{...r,children:(0,w.jsx)(m.InnerBlockLayoutContextProvider,{parentName:"woocommerce/add-to-cart-with-options-grouped-product-selector-item",children:(0,w.jsx)("div",{role:"list",children:c?.map((e=>(0,w.jsx)(b,{attributes:{productId:e.id},blocks:a,isSelected:(l||c[0]?.id)===e.id,onSelect:()=>p(e.id)},e.id)))})})})},attributes:i.attributes,icon:{src:(0,w.jsx)(s.A,{icon:c.A,className:"wc-block-editor-components-block-icon"})},save:function(){const e=d.useBlockProps.save(),t=d.useInnerBlocksProps.save({...e,role:"listitem"});return(0,w.jsx)("div",{...t})}})},1609:e=>{e.exports=window.React},790:e=>{e.exports=window.ReactJSXRuntime},9491:e=>{e.exports=window.wp.compose},6087:e=>{e.exports=window.wp.element},5573:e=>{e.exports=window.wp.primitives}},s={};function c(e){var t=s[e];if(void 0!==t)return t.exports;var o=s[e]={exports:{}};return r[e].call(o.exports,o,o.exports,c),o.exports}c.m=r,e=[],c.O=(t,o,r,s)=>{if(!o){var i=1/0;for(l=0;l<e.length;l++){for(var[o,r,s]=e[l],n=!0,d=0;d<o.length;d++)(!1&s||i>=s)&&Object.keys(c.O).every((e=>c.O[e](o[d])))?o.splice(d--,1):(n=!1,s<i&&(i=s));if(n){e.splice(l--,1);var a=r();void 0!==a&&(t=a)}}return t}s=s||0;for(var l=e.length;l>0&&e[l-1][2]>s;l--)e[l]=e[l-1];e[l]=[o,r,s]},c.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return c.d(t,{a:t}),t},o=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,c.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var s=Object.create(null);c.r(s);var i={};t=t||[null,o({}),o([]),o(o)];for(var n=2&r&&e;"object"==typeof n&&!~t.indexOf(n);n=o(n))Object.getOwnPropertyNames(n).forEach((t=>i[t]=()=>e[t]));return i.default=()=>e,c.d(s,i),s},c.d=(e,t)=>{for(var o in t)c.o(t,o)&&!c.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},c.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),c.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},c.j=4638,(()=>{var e={4638:0};c.O.j=t=>0===e[t];var t=(t,o)=>{var r,s,[i,n,d]=o,a=0;if(i.some((t=>0!==e[t]))){for(r in n)c.o(n,r)&&(c.m[r]=n[r]);if(d)var l=d(c)}for(t&&t(o);a<i.length;a++)s=i[a],c.o(e,s)&&e[s]&&e[s][0](),e[s]=0;return c.O(l)},o=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))})();var i=c.O(void 0,[94],(()=>c(123)));i=c.O(i),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["add-to-cart-with-options-grouped-product-selector-item"]=i})();