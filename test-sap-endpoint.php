<?php
/**
 * Test script for SAP SoldTo API endpoint
 * 
 * Usage: Place this file in your WordPress root and access via browser
 * or run via WP-CLI: wp eval-file test-sap-endpoint.php
 */

// Ensure WordPress is loaded
if (!defined('ABSPATH')) {
    // If running from browser, load WordPress
    require_once('wp-config.php');
}

function test_sap_soldto_endpoint() {
    echo "<h2>Testing SAP SoldTo API Endpoint</h2>\n";
    
    // Sample test data
    $test_data = [
        'soldTo' => [
            'customerId' => 'TEST_CUSTOMER_' . time(),
            'email' => 'test' . time() . '@example.com',
            'companyCode' => 'TEST_COMP',
            'countryCode' => 'US',
            'priceGroup' => 'STANDARD'
        ],
        'billingAddress' => [
            'company' => 'Test Company Inc.',
            'address' => [
                'line1' => '123 Test Street',
                'line2' => 'Suite 456',
                'city' => 'Test City',
                'postcode' => '12345',
                'countryRegion' => 'US',
                'stateCounty' => 'CA'
            ]
        ],
        'shiptos' => ['SHIPTO001', 'SHIPTO002']
    ];
    
    echo "<h3>Test Data:</h3>\n";
    echo "<pre>" . print_r($test_data, true) . "</pre>\n";
    
    // Create a mock WP_REST_Request
    $request = new WP_REST_Request('POST', '/wc/v3/sap-soldto');
    $request->set_header('content-type', 'application/json');
    $request->set_body(json_encode($test_data));
    
    // Test the endpoint function directly
    echo "<h3>Testing Endpoint Function:</h3>\n";
    
    // Check if function exists
    if (!function_exists('cuc_create_customer_endpoint')) {
        echo "<p style='color: red;'>❌ Function cuc_create_customer_endpoint not found! Make sure the plugin is loaded.</p>\n";
        return;
    }
    
    // Test with admin privileges
    wp_set_current_user(1); // Assume user ID 1 is admin
    
    $result = cuc_create_customer_endpoint($request);
    
    echo "<h3>Result:</h3>\n";
    if (is_wp_error($result)) {
        echo "<p style='color: red;'>❌ Error: " . $result->get_error_message() . "</p>\n";
        echo "<pre>Error Data: " . print_r($result->get_error_data(), true) . "</pre>\n";
    } else {
        echo "<p style='color: green;'>✅ Success!</p>\n";
        echo "<pre>" . print_r($result, true) . "</pre>\n";
        
        // Verify the user was created and metadata was saved
        if (isset($result['user_id'])) {
            $user_id = $result['user_id'];
            echo "<h3>Verifying User Metadata:</h3>\n";
            
            $meta_keys = [
                'customer_id', 'company_code', 'country_code', 'price_group',
                'billing_company', 'billing_address_1', 'billing_city', 
                'billing_postcode', 'billing_country', 'shiptos'
            ];
            
            foreach ($meta_keys as $key) {
                $value = get_user_meta($user_id, $key, true);
                $status = !empty($value) ? "✅" : "❌";
                echo "<p>{$status} {$key}: " . print_r($value, true) . "</p>\n";
            }
        }
    }
}

// Run the test
test_sap_soldto_endpoint();
?>
