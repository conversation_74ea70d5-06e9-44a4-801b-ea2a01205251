import*as e from"@wordpress/interactivity";var t={301:()=>{},438:e=>{e.exports=import("@wordpress/interactivity-router")}},o={};function r(e){var n=o[e];if(void 0!==n)return n.exports;var c=o[e]={exports:{}};return t[e](c,c.exports,r),c.exports}r.d=(e,t)=>{for(var o in t)r.o(t,o)&&!r.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);const n=(l={getContext:()=>e.getContext,getElement:()=>e.getElement,store:()=>e.store},s={},r.d(s,l),s),c=(e,{bubbles:t=!1,cancelable:o=!1,element:r,detail:n={}})=>{if(!CustomEvent)return;r||(r=document.body);const c=new CustomEvent(e,{bubbles:t,cancelable:o,detail:n});r.dispatchEvent(c)},i=e=>{c("wc-blocks_product_list_rendered",{bubbles:!0,cancelable:!0,detail:e})};var l,s;function a(e){return null!==e&&e instanceof window.HTMLAnchorElement&&!!e.href&&(!e.target||"_self"===e.target)&&e.origin===window.location.origin}r(301);(0,n.store)("woocommerce/product-collection",{actions:{*navigate(e){const{ref:t}=(0,n.getElement)();if(a(t)&&function(e){return!(0!==e.button||e.metaKey||e.ctrlKey||e.altKey||e.shiftKey||e.defaultPrevented)}(e)){e.preventDefault();const o=(0,n.getContext)(),c=t.closest("[data-wp-router-region]")?.getAttribute("data-wp-router-region"),{actions:l}=yield Promise.resolve().then(r.bind(r,438));yield l.navigate(t.href),o.isPrefetchNextOrPreviousLink=t.href;const s=document.querySelector(`[data-wp-router-region=${c}] .wc-block-product-template .wc-block-product a`);s?.focus(),i({collection:o.collection})}},*prefetchOnHover(){const{ref:e}=(0,n.getElement)();if(a(e)){const{actions:t}=yield Promise.resolve().then(r.bind(r,438));yield t.prefetch(e.href)}},*viewProduct(){const{collection:e,productId:t}=(0,n.getContext)();t&&c("wc-blocks_viewed_product",{bubbles:!0,cancelable:!0,detail:{collection:e,productId:t}})}},callbacks:{*prefetch(){const{ref:e}=(0,n.getElement)(),t=(0,n.getContext)();if(a(e)&&t.isPrefetchNextOrPreviousLink){const{actions:t}=yield Promise.resolve().then(r.bind(r,438));yield t.prefetch(e.href)}},*onRender(){const{collection:e}=(0,n.getContext)();i({collection:e})}}},{lock:!0});