(()=>{"use strict";var e={n:o=>{var t=o&&o.__esModule?()=>o.default:()=>o;return e.d(t,{a:t}),t},d:(o,t)=>{for(var n in t)e.o(t,n)&&!e.o(o,n)&&Object.defineProperty(o,n,{enumerable:!0,get:t[n]})},o:(e,o)=>Object.prototype.hasOwnProperty.call(e,o)};const o=window.wp.i18n,t=window.wp.data,n=window.wp.domReady;var c=e.n(n);const d=window.wc.wcSettings;c()((()=>{(0,t.dispatch)("core/notices").createSuccessNotice((0,o.__)("Sample products added","woocommerce"),{id:"WOOCOMMERCE_ONBOARDING_LOAD_SAMPLE_PRODUCTS_NOTICE",actions:[{url:(0,d.getAdminLink)("admin.php?page=wc-admin"),label:(0,o.__)("Continue setting up your store","woocommerce")}]})})),(window.wc=window.wc||{}).onboardingLoadSampleProductsNotice={}})();