(()=>{"use strict";var e,o,t,r={1528:(e,o,t)=>{const r=window.wp.blocks;var c=t(4530),i=t(6012);const n=JSON.parse('{"name":"woocommerce/add-to-cart-with-options-grouped-product-selector","title":"Grouped Product Selector (Experimental)","description":"Display a group of products that can be added to the cart.","category":"woocommerce-product-elements","keywords":["WooCommerce"],"usesContext":["postId"],"ancestor":["woocommerce/add-to-cart-with-options"],"textdomain":"woocommerce","apiVersion":3,"supports":{"interactivity":true},"$schema":"https://schemas.wp.org/trunk/block.json"}'),s=window.wp.blockEditor,a=[["woocommerce/add-to-cart-with-options-grouped-product-selector-item",{},[["core/group",{layout:{type:"flex",orientation:"horizontal",flexWrap:"nowrap"},style:{spacing:{margin:{top:"1rem",bottom:"1rem"}}}},[["woocommerce/add-to-cart-with-options-grouped-product-selector-item-cta"],["core/post-title",{level:2,fontSize:"medium",style:{layout:{selfStretch:"fill"},spacing:{margin:{top:"0",bottom:"0"}},typography:{fontWeight:400}},isLink:!0}],["core/group",{layout:{type:"flex",orientation:"vertical"},style:{spacing:{blockGap:"0"}}},[["woocommerce/product-price",{isDescendentOfSingleProductBlock:!0,fontSize:"medium",textAlign:"right",style:{typography:{fontWeight:400}}}],["woocommerce/product-stock-indicator"]]]]]]]];var p=t(790);const l=window.wc.wcSettings,d=window.wc.wcTypes,u=(0,l.getSettingWithCoercion)("isBlockifiedAddToCart",!1,d.isBoolean),m=(0,l.getSetting)("isBlockTheme");u&&m&&(0,r.registerBlockType)(n,{edit:function(e){const{className:o}=e.attributes,t=(0,s.useBlockProps)({className:o}),r=(0,s.useInnerBlocksProps)(t,{template:a,templateLock:"all"});return(0,p.jsx)("div",{...r})},attributes:n.attributes,icon:{src:(0,p.jsx)(c.A,{icon:i.A,className:"wc-block-editor-components-block-icon"})},save:function(){const e=s.useBlockProps.save(),o=s.useInnerBlocksProps.save({...e,role:"list"});return(0,p.jsx)("div",{...o})}})},1609:e=>{e.exports=window.React},790:e=>{e.exports=window.ReactJSXRuntime},6087:e=>{e.exports=window.wp.element},5573:e=>{e.exports=window.wp.primitives}},c={};function i(e){var o=c[e];if(void 0!==o)return o.exports;var t=c[e]={exports:{}};return r[e].call(t.exports,t,t.exports,i),t.exports}i.m=r,e=[],i.O=(o,t,r,c)=>{if(!t){var n=1/0;for(l=0;l<e.length;l++){for(var[t,r,c]=e[l],s=!0,a=0;a<t.length;a++)(!1&c||n>=c)&&Object.keys(i.O).every((e=>i.O[e](t[a])))?t.splice(a--,1):(s=!1,c<n&&(n=c));if(s){e.splice(l--,1);var p=r();void 0!==p&&(o=p)}}return o}c=c||0;for(var l=e.length;l>0&&e[l-1][2]>c;l--)e[l]=e[l-1];e[l]=[t,r,c]},i.n=e=>{var o=e&&e.__esModule?()=>e.default:()=>e;return i.d(o,{a:o}),o},t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,i.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var c=Object.create(null);i.r(c);var n={};o=o||[null,t({}),t([]),t(t)];for(var s=2&r&&e;"object"==typeof s&&!~o.indexOf(s);s=t(s))Object.getOwnPropertyNames(s).forEach((o=>n[o]=()=>e[o]));return n.default=()=>e,i.d(c,n),c},i.d=(e,o)=>{for(var t in o)i.o(o,t)&&!i.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:o[t]})},i.o=(e,o)=>Object.prototype.hasOwnProperty.call(e,o),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.j=2,(()=>{var e={2:0};i.O.j=o=>0===e[o];var o=(o,t)=>{var r,c,[n,s,a]=t,p=0;if(n.some((o=>0!==e[o]))){for(r in s)i.o(s,r)&&(i.m[r]=s[r]);if(a)var l=a(i)}for(o&&o(t);p<n.length;p++)c=n[p],i.o(e,c)&&e[c]&&e[c][0](),e[c]=0;return i.O(l)},t=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];t.forEach(o.bind(null,0)),t.push=o.bind(null,t.push.bind(t))})();var n=i.O(void 0,[94],(()=>i(1528)));n=i.O(n),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["add-to-cart-with-options-grouped-product-selector"]=n})();