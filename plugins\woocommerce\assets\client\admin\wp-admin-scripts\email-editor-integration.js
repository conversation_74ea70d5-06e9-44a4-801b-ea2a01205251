/*! For license information please see email-editor-integration.js.LICENSE.txt */
(()=>{"use strict";var e={94931:(e,t,o)=>{var r=o(51609),n=Symbol.for("react.element"),i=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,s=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function l(e,t,o){var r,i={},l=null,d=null;for(r in void 0!==o&&(l=""+o),void 0!==t.key&&(l=""+t.key),void 0!==t.ref&&(d=t.ref),t)a.call(t,r)&&!c.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===i[r]&&(i[r]=t[r]);return{$$typeof:n,type:e,key:l,ref:d,props:i,_owner:s.current}}t.Fragment=i,t.jsx=l,t.jsxs=l},39793:(e,t,o)=>{e.exports=o(94931)},51609:e=>{e.exports=window.React}},t={};const o=window.wp.hooks,r=window.wp.blocks,n=window.wp.i18n,i=window.wp.element,a=window.wp.data,s=window.wp.components,c=window.wp.editor;var l=function o(r){var n=t[r];if(void 0!==n)return n.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,o),i.exports}(39793);function d(){return(0,l.jsx)("div",{style:{margin:"20vh auto",maxWidth:400,padding:20,backgroundColor:"#fff",borderRadius:4,boxShadow:"0 0 10px 0 rgba(0, 0, 0, 0.1)",textAlign:"center",color:"#000"},children:(0,l.jsxs)(s.__experimentalText,{children:[" ",(0,n.__)("Autogenerated content","woocommerce")," "]})})}const m=(e,t)=>{e?.current?.contentWindow?.document.body&&(e.current.contentWindow.document.body.style.overflow="hidden",e.current.contentWindow.document.body.style.pointerEvents="none",e.current.contentWindow.document.body.style.backgroundColor=t?"#00000059":e.current.contentWindow?.document?.bgColor)};function u(){const{postSlug:e}=(0,a.useSelect)((e=>({postSlug:e(c.store).getCurrentPost()?.slug})),[]),t=(0,i.useRef)(null),[o,r]=(0,i.useState)(!1),s=window.WooCommerceEmailEditor?.block_preview_url;return(0,i.useEffect)((()=>{if(!e)return;const o=(r=e||"",window.WooCommerceEmailEditor.email_types.find((e=>e.value===r))?.id);var r;o&&t.current&&((e,t)=>{e?.current?.contentWindow?.location.replace(t)})(t,`${s}&type=${o}`)}),[e,t,s]),(0,l.jsxs)("div",{style:{position:"relative"},children:[(0,l.jsx)("iframe",{style:{width:"100%",height:t?.current?.contentWindow?.document?.body?.clientHeight||"750px",backgroundColor:"initial",minHeight:"100px"},ref:t,src:`${s}&type=WC_Email_Customer_Processing_Order`,title:(0,n.__)("Email preview frame","woocommerce"),onMouseEnter:()=>{r(!0),m(t,!0)},onMouseLeave:()=>{r(!1),m(t,!1)}}),o&&(0,l.jsx)("div",{style:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",zIndex:1e3,pointerEvents:"none"},children:(0,l.jsx)(d,{})})]})}const _={title:(0,n.__)("Woo Email Content","woocommerce"),category:"text",attributes:{},edit:function(){return(0,l.jsx)(u,{})},save:function(){return(0,l.jsx)("div",{children:"##WOO_CONTENT##"})}},p="woocommerce/email-editor-integration",w=window.wp.coreData;function f(){const[e,t]=(0,w.useEntityProp)("postType","wp_template","woocommerce_data"),o=(0,i.useRef)(null),r=(0,i.useCallback)((o=>{t({...e,sender_settings:{...e?.sender_settings,from_name:o}})}),[e,t]),a=(0,i.useCallback)((r=>{t({...e,sender_settings:{...e?.sender_settings,from_address:r}}),o.current&&(o.current.checkValidity(),o.current.reportValidity())}),[e,t]);return(0,l.jsx)(s.Panel,{className:"woocommerce-email-sidebar-template-settings-sender-options",children:(0,l.jsxs)(s.PanelBody,{children:[(0,l.jsx)(s.PanelRow,{children:(0,l.jsxs)("div",{children:[(0,l.jsx)("h2",{children:(0,n.__)("Sender Options","woocommerce")}),(0,l.jsx)("p",{children:(0,n.__)("This is how your sender name and email address would appear in outgoing WooCommerce emails.","woocommerce")})]})}),(0,l.jsx)(s.PanelRow,{children:(0,l.jsx)(s.TextControl,{className:"woocommerce-email-sidebar-template-settings-sender-options-input",label:(0,n.__)("“from” name","woocommerce"),name:"from_name",type:"text",value:e?.sender_settings?.from_name||"",onChange:r})}),(0,l.jsx)(s.PanelRow,{children:(0,l.jsx)(s.TextControl,{ref:o,className:"woocommerce-email-sidebar-template-settings-sender-options-input",label:(0,n.__)("“from” email","woocommerce"),name:"from_email",type:"email",value:e?.sender_settings?.from_address||"",onChange:a,required:!0})})]})})}function h(e){var t,o,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var n=e.length;for(t=0;t<n;t++)e[t]&&(o=h(e[t]))&&(r&&(r+=" "),r+=o)}else for(o in e)e[o]&&(r&&(r+=" "),r+=o);return r}const b=function(){for(var e,t,o=0,r="",n=arguments.length;o<n;o++)(e=arguments[o])&&(t=h(e))&&(r&&(r+=" "),r+=t);return r},x=({RichTextWithButton:e})=>{var t;const[o]=(0,w.useEntityProp)("postType","woo_email","woocommerce_data"),r=(e,t)=>{const o=(0,a.select)(w.store).getEditedEntityRecord("postType","woo_email",window.WooCommerceEmailEditor.current_post_id),r=o?.woocommerce_data||{};(0,a.dispatch)(w.store).editEntityRecord("postType","woo_email",window.WooCommerceEmailEditor.current_post_id,{woocommerce_data:{...r,[e]:t}})},i=null!==(t=o?.preheader?.length)&&void 0!==t?t:0;return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("br",{}),"customer_refunded_order"===o.email_type?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(e,{attributeName:"subject_full",attributeValue:o.subject_full,updateProperty:r,label:(0,n.__)("Full Refund Subject","woocommerce"),placeholder:o.default_subject}),(0,l.jsx)("br",{}),(0,l.jsx)(e,{attributeName:"subject_partial",attributeValue:o.subject_partial,updateProperty:r,label:(0,n.__)("Partial Refund Subject","woocommerce"),placeholder:o.default_subject})]}):(0,l.jsx)(e,{attributeName:"subject",attributeValue:o.subject,updateProperty:r,label:(0,n.__)("Subject","woocommerce"),placeholder:o.default_subject}),(0,l.jsx)("br",{}),(0,l.jsx)(e,{attributeName:"preheader",attributeValue:o.preheader,updateProperty:r,label:(0,n.__)("Preview text","woocommerce"),help:(0,l.jsxs)("span",{className:b("woocommerce-settings-panel__preview-text-length",{"woocommerce-settings-panel__preview-text-length-warning":i>80,"woocommerce-settings-panel__preview-text-length-error":i>150}),children:[i,"/",150]}),placeholder:(0,n.__)("Shown as a preview in the inbox, next to the subject line.","woocommerce")})]})};(0,o.addFilter)("woocommerce_email_editor_send_button_label",p,(()=>(0,n.__)("Save email","woocommerce"))),(0,o.addFilter)("woocommerce_email_editor_content_validation_rules",p,(e=>[...e||[],{id:"sender-email-validation",testContent:()=>{const e=document.querySelector('input[name="from_email"]'),t=e?.value;return!(!t||t&&e?.checkValidity())},message:(0,n.__)('The "from" email address is invalid. Please enter a valid email address that will appear as the sender in outgoing WooCommerce emails.',"woocommerce"),actions:[]}])),(0,o.addFilter)("woocommerce_email_editor_check_sending_method_configuration_link",p,(()=>"https://woocommerce.com/document/email-faq/")),(0,r.registerBlockType)("woo/email-content",_),(0,o.addFilter)("woocommerce_email_editor_setting_sidebar_extension_component",p,(e=>()=>(0,l.jsx)(x,{RichTextWithButton:e}))),(0,o.addFilter)("woocommerce_email_editor_template_sections","my-plugin/template-settings",(e=>[...e,{id:"my-custom-section",render:()=>(0,l.jsx)(f,{})}])),(window.wc=window.wc||{}).emailEditorIntegration={}})();