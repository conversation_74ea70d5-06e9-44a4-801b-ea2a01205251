{"name": "woocommerce/mini-cart", "version": "1.0.0", "title": "Mini-Cart", "icon": "miniCartAlt", "description": "Display a button for shoppers to quickly view their cart.", "category": "woocommerce", "keywords": ["WooCommerce"], "textdomain": "woocommerce", "supports": {"html": false, "multiple": false, "typography": {"fontSize": true}}, "example": {"attributes": {"isPreview": true, "className": "wc-block-mini-cart--preview"}}, "attributes": {"isPreview": {"type": "boolean", "default": false}, "miniCartIcon": {"type": "string", "default": "cart"}, "addToCartBehaviour": {"type": "string", "default": "none"}, "onCartClickBehaviour": {"type": "string", "default": "open_drawer"}, "hasHiddenPrice": {"type": "boolean", "default": true}, "cartAndCheckoutRenderStyle": {"type": "string", "default": "hidden"}, "priceColor": {"type": "object"}, "priceColorValue": {"type": "string"}, "iconColor": {"type": "object"}, "iconColorValue": {"type": "string"}, "productCountColor": {"type": "object"}, "productCountColorValue": {"type": "string"}, "productCountVisibility": {"type": "string", "default": "greater_than_zero"}}, "apiVersion": 3, "$schema": "https://schemas.wp.org/trunk/block.json"}