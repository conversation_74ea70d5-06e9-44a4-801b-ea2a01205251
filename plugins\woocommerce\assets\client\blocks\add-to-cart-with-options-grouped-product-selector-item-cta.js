(()=>{var t,e,o,r={5723:(t,e,o)=>{"use strict";const r=window.wp.blocks;var c=o(4530),n=o(6012);const s=JSON.parse('{"name":"woocommerce/add-to-cart-with-options-grouped-product-selector-item-cta","title":"Grouped Product Selector Item CTA (Experimental)","description":"A CTA for a child product within the Grouped Product Selector block. Depending on the product type and properties, this might be a button, a checkbox or a link.","category":"woocommerce-product-elements","keywords":["WooCommerce"],"usesContext":["postId"],"ancestor":["woocommerce/add-to-cart-with-options-grouped-product-selector-item"],"textdomain":"woocommerce","apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json","supports":{"inserter":false,"interactivity":true},"style":"file:../woocommerce/add-to-cart-with-options-grouped-product-selector-item-cta-style.css"}'),i=window.wc.wcBlocksSharedContext,a=window.wp.components,l=window.wp.blockEditor;var d=o(7723),p=o(790);const u=()=>(0,p.jsxs)("div",{className:"quantity wc-block-components-quantity-selector",children:[(0,p.jsx)("button",{className:"wc-block-components-quantity-selector__button wc-block-components-quantity-selector__button--minus",children:"-"}),(0,p.jsx)("input",{type:"number",value:"1",className:"input-text qty text",readOnly:!0}),(0,p.jsx)("button",{className:"wc-block-components-quantity-selector__button wc-block-components-quantity-selector__button--plus",children:"+"})]}),w=()=>{const{isLoading:t,product:e}=(0,i.useProductDataContext)();if(t)return(0,p.jsx)(a.Spinner,{});const{permalink:o,add_to_cart:r,has_options:c,is_purchasable:n,is_in_stock:s,sold_individually:l}=e;return!c&&n&&s?l?(0,p.jsx)("input",{type:"checkbox",value:"1",className:"wc-grouped-product-add-to-cart-checkbox"}):(0,p.jsx)(u,{}):(0,p.jsx)("a",{"aria-label":r?.description||"",className:"button wp-element-button add_to_cart_button wc-block-components-product-button__button",href:o,children:r?.text||(0,d.__)("Add to Cart","woocommerce")})},b=window.wc.wcSettings,m=window.wc.wcTypes,h=(0,b.getSettingWithCoercion)("isBlockifiedAddToCart",!1,m.isBoolean),f=(0,b.getSetting)("isBlockTheme"),k=h&&f;o(5654),k&&(0,r.registerBlockType)(s,{edit:function(){const t=(0,l.useBlockProps)({className:"wc-block-add-to-cart-with-options-grouped-product-selector-item-cta"});return(0,p.jsx)("div",{...t,children:(0,p.jsx)(a.Disabled,{children:(0,p.jsx)(w,{})})})},attributes:s.attributes,icon:{src:(0,p.jsx)(c.A,{icon:n.A,className:"wc-block-editor-components-block-icon"})},save:()=>null})},5654:()=>{},1609:t=>{"use strict";t.exports=window.React},790:t=>{"use strict";t.exports=window.ReactJSXRuntime},6087:t=>{"use strict";t.exports=window.wp.element},7723:t=>{"use strict";t.exports=window.wp.i18n},5573:t=>{"use strict";t.exports=window.wp.primitives}},c={};function n(t){var e=c[t];if(void 0!==e)return e.exports;var o=c[t]={exports:{}};return r[t].call(o.exports,o,o.exports,n),o.exports}n.m=r,t=[],n.O=(e,o,r,c)=>{if(!o){var s=1/0;for(d=0;d<t.length;d++){for(var[o,r,c]=t[d],i=!0,a=0;a<o.length;a++)(!1&c||s>=c)&&Object.keys(n.O).every((t=>n.O[t](o[a])))?o.splice(a--,1):(i=!1,c<s&&(s=c));if(i){t.splice(d--,1);var l=r();void 0!==l&&(e=l)}}return e}c=c||0;for(var d=t.length;d>0&&t[d-1][2]>c;d--)t[d]=t[d-1];t[d]=[o,r,c]},n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e},o=Object.getPrototypeOf?t=>Object.getPrototypeOf(t):t=>t.__proto__,n.t=function(t,r){if(1&r&&(t=this(t)),8&r)return t;if("object"==typeof t&&t){if(4&r&&t.__esModule)return t;if(16&r&&"function"==typeof t.then)return t}var c=Object.create(null);n.r(c);var s={};e=e||[null,o({}),o([]),o(o)];for(var i=2&r&&t;"object"==typeof i&&!~e.indexOf(i);i=o(i))Object.getOwnPropertyNames(i).forEach((e=>s[e]=()=>t[e]));return s.default=()=>t,n.d(c,s),c},n.d=(t,e)=>{for(var o in e)n.o(e,o)&&!n.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.j=3643,(()=>{var t={3643:0};n.O.j=e=>0===t[e];var e=(e,o)=>{var r,c,[s,i,a]=o,l=0;if(s.some((e=>0!==t[e]))){for(r in i)n.o(i,r)&&(n.m[r]=i[r]);if(a)var d=a(n)}for(e&&e(o);l<s.length;l++)c=s[l],n.o(t,c)&&t[c]&&t[c][0](),t[c]=0;return n.O(d)},o=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];o.forEach(e.bind(null,0)),o.push=e.bind(null,o.push.bind(o))})();var s=n.O(void 0,[94],(()=>n(5723)));s=n.O(s),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["add-to-cart-with-options-grouped-product-selector-item-cta"]=s})();