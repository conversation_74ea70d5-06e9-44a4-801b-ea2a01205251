{"name": "woocommerce/stock-filter", "title": "Filter by Stock Controls", "description": "Enable customers to filter the product grid by stock status.", "category": "woocommerce", "keywords": ["WooCommerce"], "supports": {"interactivity": {"clientNavigation": false}, "html": false, "multiple": false, "color": {"background": true, "text": true, "button": true}, "inserter": false, "lock": false}, "attributes": {"className": {"type": "string", "default": ""}, "headingLevel": {"type": "number", "default": 3}, "showCounts": {"type": "boolean", "default": false}, "showFilterButton": {"type": "boolean", "default": false}, "displayStyle": {"type": "string", "default": "list"}, "selectType": {"type": "string", "default": "multiple"}, "isPreview": {"type": "boolean", "default": false}}, "textdomain": "woocommerce", "apiVersion": 3, "$schema": "https://schemas.wp.org/trunk/block.json"}