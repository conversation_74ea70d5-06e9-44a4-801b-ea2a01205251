(()=>{"use strict";const e=window.wc.tracks,t=document.querySelector('[name="add_new_attribute"]'),r=document.querySelector('[name="save_attribute"]'),c=document.querySelectorAll(".row-actions span a"),u=document.querySelectorAll(".configure-terms");t?.addEventListener("click",(function(){const t=document.querySelector("#attribute_public"),r=document.querySelector("#attribute_orderby"),c=document.querySelector("#attribute_label"),u=document.querySelector("#attribute_name");(0,e.recordEvent)("product_attributes_add",{enable_archive:t?.checked?"yes":"no",default_sort_order:r?.value,name:c?.value,slug:u?.value,page:"attributes"})})),r?.addEventListener("click",(function(){const t=document.querySelector("#attribute_public"),r=document.querySelector("#attribute_orderby");(0,e.recordEvent)("product_attributes_update",{enable_archive:t?.checked?"yes":"no",default_sort_order:r?.value,page:"attributes"})})),c.forEach((t=>{t.addEventListener("click",(function(t){const r=t.target.parentElement.classList[0],c={edit:"edit",delete:"delete"};c[r]&&(0,e.recordEvent)("product_attributes_"+c[r],{page:"attributes"})}))})),u.forEach((t=>{t.addEventListener("click",(function(){(0,e.recordEvent)("product_attributes_configure_terms",{page:"attributes"})}))})),(window.wc=window.wc||{}).attributesTracking={}})();